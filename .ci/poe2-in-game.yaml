version: "v2.0"
name: "poe2-in-game: deploy/release"
on:
  push:
    branches: [ "test", "master", "dev" ]
    paths: [ "src/*", "package.json", "vite.config.ts", "index.html", "vitest.config.mts"]
resources:
  repositories:
    - repository: wegame/workflow/ci-templates
      name: sharedTemplates
extends:
  template: vite_v2.yml@sharedTemplates
  parameters:
    MAIN_BRANCH: master,test,dev
    WEB_CI_TAG: node18
