// implement array.groupBy
export function groupBy<T>(
  arr: T[],
  key: ((item: T) => string | number) | keyof T
): Record<string | number, T[]> {
  const result: Record<string | number, T[]> = {};
  arr.forEach(item => {
    const keyValue = (key instanceof Function ? key(item) : item[key]) as
      | string
      | number;
    if (!result[keyValue]) {
      result[keyValue] = [];
    }
    result[keyValue].push(item);
  });
  return result;
}
