import mitt from 'mitt';

// 面板状态
export type PanelStates = {
  ui_states?: Record<string, boolean>;
};

// 所有状态信息
export type UserStats = {
  all_stats?: Record<string, any> & {
    character?: {
      ascendancy?: string;
      level?: string;
      class?: string;
    };
  };
};

export type UserItems = {
  all_items?: Array<unknown>;
};

export type UserSkills = {
  all_skills?: Array<unknown>;
};

export type UserPassives = {
  all_passives?: Array<unknown>;
};

export type UserAllData = UserItems & UserSkills & UserPassives & UserStats;

// 选择职业面板
export type JobPanel = {
  character_creation?: {
    selected_class: number | null;
    selected_class_name: string;
  };
};

export type BroadcastPayload<T = {}> = {
  event_type?: string;
  user_data?: string; // request返回结构
  character_name?: string; // 角色名称
} & T;
export type BroadcastEventType = {
  Msg_POE2InGame_NoticeGameMsg: BroadcastPayload;
  Msg_POE2InGame_ResolutionChange: {
    width: number;
    height: number;
  };
  Msg_POE2InGame_NoticeEnterEndgame: {
    map_level?: number;
  };
  Msg_POE2InGame_ADCVANCE: {};
  Msg_POE2InGame_NoticeBossStrategy: string;
  // F1键盘事件
  Msg_POE2InGame_SwitchHelperPanel: string;
  // 客户端票据通知
  Msg_POE2InGame_NoticeLoginTicket: {
    ticket: string;
  };
};

export type ServiceCallEventType = {
  Srv_POE2InGame_SendGameMsg: {
    msg: string;
  };
  // 点击装备助手按钮
  Srv_POE2InGame_ClickHelperBtn: string;
  Srv_POE2InGame_SetPassiveSkill: string;
};

export type ServiceCallbackEventType = {
  Srv_POE2InGame_GetLoginTicket: {
    params: {};
    result: {
      ticket: string;
    };
  };
};

export type ServiceRequestEventType = {
  Srv_POE2InGame_ReqAllStats: {
    params: {};
    result: BroadcastPayload<UserStats>;
  };
  Srv_POE2InGame_ReqAllItems: {
    params: {};
    result: BroadcastPayload<UserItems>;
  };
  Srv_POE2InGame_ReqAllSkills: {
    params: {};
    result: BroadcastPayload<UserSkills>;
  };
  Srv_POE2InGame_ReqAllPassives: {
    params: {};
    result: BroadcastPayload<UserPassives>;
  };
  Srv_POE2InGame_ReqAllGameData: {
    params: {};
    result: BroadcastPayload<UserAllData>;
  };
};

export type LocalEventType = {
  'new-session-created': {
    sessionId: string;
    title: string;
  };
};

// 本地调试或者跑测试用，正式环境不要使用这个
export const emitter = mitt<BroadcastEventType & LocalEventType>();
