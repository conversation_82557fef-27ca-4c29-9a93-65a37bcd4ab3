import { isWeGame, service } from '@tencent/wegame-web-sdk';
import {
  emitter,
  ServiceCallbackEventType,
  ServiceRequestEventType,
  BroadcastPayload,
  type ServiceCallEventType
} from '@src/util/event';
import { log } from '@src/util/log';

export function callService<K extends keyof ServiceCallEventType>(
  cmd: K,
  data: ServiceCallEventType[K]
) {
  log(`callService ${cmd} params: ${JSON.stringify(data)}`);
  service.call(cmd, data);
}

function guid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export function callServiceWithCallback<
  K extends keyof ServiceCallbackEventType
>(
  cmd: K,
  data: ServiceCallbackEventType[K]['params'] = {}
): Promise<ServiceCallbackEventType[K]['result']> {
  log(`callServiceWithCallback ${cmd} params: ${JSON.stringify(data)}`);

  let timeoutTimer: null | ReturnType<typeof setTimeout> = null;

  return new Promise((resolve, reject) => {
    timeoutTimer = setTimeout(() => {
      log(`callServiceWithCallback ${cmd} timeout`);
      reject('timeout');
    }, 2000); // 兜底超时时间2s

    service.call(cmd, data, (result: ServiceCallbackEventType[K]['result']) => {
      timeoutTimer && clearTimeout(timeoutTimer);
      log(`callServiceWithCallback ${cmd} result: ${JSON.stringify(result)}`);
      resolve(result);
    });
  });
}

const callbackMapping: Record<string, (data: any) => void> = {};

let serviceRequestListenerInited = false;

export function initServiceRequestCallbackLisntener() {
  if (serviceRequestListenerInited) {
    console.warn('service request callback listener already inited');
    return;
  }
  serviceRequestListenerInited = true;

  const handler = (payload: BroadcastPayload) => {
    if (payload.event_type !== 'Response') {
      return;
    }
    if (callbackMapping[payload.user_data!]) {
      callbackMapping[payload.user_data!](payload);
      delete callbackMapping[payload.user_data!];
    }
  };

  if (isWeGame) {
    service.listen<{ msg: string }>('Msg_POE2InGame_NoticeGameMsg', ev => {
      const events: Array<BroadcastPayload> = JSON.parse(ev.msg);
      events.forEach(e => {
        handler(e);
      });
    });
  } else {
    emitter.on('Msg_POE2InGame_NoticeGameMsg', handler);
  }
}

export function serviceRequest<K extends keyof ServiceRequestEventType>(
  cmd: K,
  data: ServiceRequestEventType[K]['params'] = {}
): Promise<ServiceRequestEventType[K]['result']> {
  log(`serviceRequest ${cmd} params: ${JSON.stringify(data)}`);
  let timeoutTimer: null | ReturnType<typeof setTimeout> = null;
  return new Promise((resolve, reject) => {
    const reqId = guid();

    timeoutTimer = setTimeout(() => {
      log(`serviceRequest ${cmd} timeout`);
      reject('timeout');
    }, 2000); // 兜底超时时间2s

    service.call(cmd, {
      req_id: reqId,
      ...data
    });

    const handler = (result: ServiceRequestEventType[K]['result']) => {
      log(`serviceRequest ${cmd} result: ${JSON.stringify(result)}`);
      timeoutTimer && clearTimeout(timeoutTimer);
      resolve(result);
    };

    callbackMapping[reqId] = handler;
    // TODO: 处理开发mock逻辑
  });
}
