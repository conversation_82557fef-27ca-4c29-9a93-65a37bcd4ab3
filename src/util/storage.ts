export const setObjLocalStorageItem = (key: string, value: object) => {
  if (!key) return;
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch {
    localStorage.clear();
    localStorage.setItem(key, JSON.stringify(value));
  }
};

export const getObjLocalStorageItem = (key: string) => {
  if (!key || typeof localStorage === 'undefined') return null;
  const data = localStorage.getItem(key);
  if (!data) return null;
  try {
    return JSON.parse(data);
  } catch {
    return null;
  }
};

export const updateObjLocalStorageItem = (key: string, record: object) => {
  const currentData = getObjLocalStorageItem(key) || {};
  const newData = { ...currentData, ...record };
  setObjLocalStorageItem(key, newData);
};
