<template>
  <Assistant
    :stage="currentTipsStage"
    :is-new="isNew"
    :boss-name="currentBossName"
    :role-id="currentRoleId"
  />
  <Equipment v-if="isEquipmentShow" />
  <Skill v-if="isSkillShow" />
  <PassiveTree v-if="isPassiveTreeShow" />
  <DebugBar />
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted } from 'vue';
import '@src/assets/scss/common/index.scss';
import Assistant from '@src/pages/assistant/index.vue';
import Equipment from '@src/pages/equipment/index.vue';
import Skill from '@src/pages/skill/index.vue';
import PassiveTree from '@src/pages/passive-tree/index.vue';
import { useBroadcast } from './composable/broadcast';
import { log } from '@src/util/log';
import { setToken } from './util/token';
import {
  callServiceWithCallback,
  initServiceRequestCallbackLisntener
} from './util/service';
import { isWeGame } from '@tencent/wegame-web-sdk';
import {
  STAGE,
  SCENE_ENTRY_RECORD_KEY,
  SCENE_ENTRY_RECORD_DEFAULT
} from '@src/model/common';
import {
  setObjLocalStorageItem,
  updateObjLocalStorageItem,
  getObjLocalStorageItem
} from './util/storage';
import { isShowChat } from '@src/composable/aiChatStage';

const DebugBar = defineAsyncComponent(
  () => import('@src/components/debug-bar.vue')
);

const isNew = ref(false);
const isEquipmentShow = ref(false);
const isSkillShow = ref(false);
const isPassiveTreeShow = ref(false);
const currentTipsStage = ref('');
const currentBossName = ref('');
const currentRoleId = ref<number | null>(null);

// 重置所有状态的函数
const resetAllStates = () => {
  isEquipmentShow.value = false;
  isSkillShow.value = false;
  isPassiveTreeShow.value = false;
};

// 触发首次场景化内容提示
const triggerFirstTimeScene = (stage: string) => {
  const sceneRecord = getObjLocalStorageItem(SCENE_ENTRY_RECORD_KEY) || {};
  // 如果是首次触发
  if (!sceneRecord[stage]) {
    currentTipsStage.value = stage; // 展示内容介绍
    updateObjLocalStorageItem(SCENE_ENTRY_RECORD_KEY, {
      [stage]: true
    });
  }
};

// 职业选择页事件处理
const handleCharacterCreationEvent = (data: any) => {
  if (data.character_creation) {
    resetAllStates();
    // 选择角色
    if (data.character_creation.selected_class_name) {
      currentRoleId.value = data.character_creation.selected_class;
      currentTipsStage.value = STAGE.ROLE_SELECT;
    } else {
      triggerFirstTimeScene(STAGE.WELCOME);
    }
  }
};

// 面板事件（装备、技能、天赋面板）
const handleCharacterPanelEvent = (data: any) => {
  const { ui_states: uiStates } = data;
  if (uiStates) {
    resetAllStates();
    if (uiStates.inventory_panel) {
      triggerFirstTimeScene(STAGE.INVENTORY_PANEL);
      isEquipmentShow.value = true;
    }
    if (uiStates.gem_skill_panel) {
      triggerFirstTimeScene(STAGE.GEM_SKILL_PANEL);
      isSkillShow.value = true;
    }
    if (uiStates.passive_skill_panel) {
      triggerFirstTimeScene(STAGE.PASSIVE_SKILL_PANEL);
      isPassiveTreeShow.value = true;
    }
    isShowChat.value = false;
  }
};

// 监听游戏消息事件
useBroadcast('Msg_POE2InGame_NoticeGameMsg', response => {
  // 遍历数组中的每个事件
  if (response.event_type === 'GamePlay') {
    log(`收到角色面板事件: ${JSON.stringify(response)}`);
    handleCharacterCreationEvent(response);
  } else if (response.event_type === 'Character') {
    log(`收到场景化事件: ${JSON.stringify(response)}`);
    handleCharacterPanelEvent(response);
  }
});

// 监听升华场景事件
useBroadcast('Msg_POE2InGame_ADCVANCE', data => {
  log(`收到升华场景事件: ${JSON.stringify(data)}`);
  resetAllStates();
  currentTipsStage.value = STAGE.ADVANCEMENT;
});

// 监听进入异界事件
useBroadcast('Msg_POE2InGame_NoticeEnterEndgame', data => {
  log(`收到进入异界事件: ${JSON.stringify(data)}`);
  resetAllStates();
  currentTipsStage.value = STAGE.OUTLAND_MAP;
});

// 监听boss三次战斗结束
useBroadcast('Msg_POE2InGame_NoticeBossStrategy', async data => {
  log(`收到boss三次战斗结束事件: ${data}`);
  resetAllStates();
  currentBossName.value = data;
  currentTipsStage.value = STAGE.BOSS_BATTLE;
});

// 监听分辨率变更事件
useBroadcast('Msg_POE2InGame_ResolutionChange', ({ width, height }) => {
  log(`Msg_POE2InGame_ResolutionChange: ${width} x ${height}`);
});

// 监听票据变更事件
useBroadcast('Msg_POE2InGame_NoticeLoginTicket', ({ ticket }) => {
  log(`Msg_POE2InGame_NoticeTicketChange: ${ticket}`);
  setToken(ticket);
});

const initToken = async () => {
  if (!isWeGame) {
    return;
  }
  // 获取票据
  const { ticket } = await callServiceWithCallback(
    'Srv_POE2InGame_GetLoginTicket'
  );
  setToken(ticket);
};

onMounted(() => {
  initToken();
  initServiceRequestCallbackLisntener();
  if (!getObjLocalStorageItem(SCENE_ENTRY_RECORD_KEY)) {
    setObjLocalStorageItem(SCENE_ENTRY_RECORD_KEY, SCENE_ENTRY_RECORD_DEFAULT);
  }
});

// const { content, reasoningContent, close } = useSSE(
//   '/api/sse/wegame.pallas.game.Poe2AI/Chat',
//   {
//     query: '战士',
//     scene: 'items'
//   }
// );
</script>
