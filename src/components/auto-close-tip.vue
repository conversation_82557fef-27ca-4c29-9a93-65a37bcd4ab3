<template>
  <div class="guide-tips-tail" @click="handleClick">
    <span v-if="hasText && countdown > 0">
      无操作 {{ countdown }}s 后关闭
    </span>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';

const props = withDefaults(
  defineProps<{
    hasText?: boolean;
    autoCloseTime?: number; // 自动关闭时间，单位毫秒
  }>(),
  {
    hasText: false,
    autoCloseTime: 15000
  }
);

const emits = defineEmits(['close']);
const intervalTimer = ref<NodeJS.Timeout | null>(null);
const countdown = ref(0);

// 启动定时器
const startTimer = () => {
  clearTimer();
  countdown.value = Math.ceil(props.autoCloseTime / 1000);
  intervalTimer.value = setInterval(() => {
    countdown.value = countdown.value - 1;
    // 倒计时结束，关闭组件
    if (countdown.value <= 0) {
      clearTimer();
      emits('close', false);
    }
  }, 1000);
};

// 清除定时器
const clearTimer = () => {
  if (intervalTimer.value) {
    clearInterval(intervalTimer.value);
    intervalTimer.value = null;
  }
};

// 点击事件处理
const handleClick = () => {
  clearTimer();
  emits('close', false);
};

// 组件挂载时启动定时器
onMounted(() => {
  startTimer();
});

// 组件卸载时清除定时器
onUnmounted(() => {
  clearTimer();
});

// 暴露方法供父组件调用
defineExpose({
  startTimer,
  clearTimer
});
</script>
