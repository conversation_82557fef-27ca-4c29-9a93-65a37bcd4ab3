<template>
  <div :class="['page-frame', `page-frame--${panelType}`]">
    <!-- 装备助手按钮 -->
    <div
      v-if="!show"
      :class="['win-client-entry', panelType]"
      @click="handleTogglePanel"
    ></div>

    <!-- 面板内容插槽 -->
    <slot v-else />
  </div>
</template>

<script lang="ts" setup>
import { useBroadcast } from '@src/composable/broadcast';
// import { callService } from '@src/util/service';

const props = defineProps<{
  panelType: string; // 面板类型
  panelName: string; // 面板名称
}>();

const show = defineModel<boolean>('show');

// 点击装备助手按钮
const handleTogglePanel = () => {
  // callService('Srv_POE2InGame_ClickHelperBtn', props.panelName);
  show.value = !show.value;
};

// 监听客户端切换面板事件（F1按键，点击助手按钮）
useBroadcast('Msg_POE2InGame_SwitchHelperPanel', panelName => {
  if (panelName === props.panelName) {
    show.value = !show.value;
  }
});
</script>
