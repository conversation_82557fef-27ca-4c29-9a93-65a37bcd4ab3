<template>
  <div v-on-click-outside="hideDropdownPop" class="planner-select-wrap">
    <div class="planner-select" @click="showDropdown = true">
      <div class="planner-item">
        <!-- data-name属性用于加渐变文字阴影，麻烦开发传入对应文本内容 -->
        <div class="planner-title" :data-name="plannerTitle">{{
          plannerTitle
        }}</div>
        <div v-if="currentItem" class="planner-tags">
          <div
            v-for="(item, index) in currentItem.tags"
            :key="index"
            class="planner-tags-item"
            >{{ item }}</div
          >
        </div>
      </div>
      <div class="planner-select-icon"></div>
    </div>
    <div v-show="showDropdown" class="planner-select-dropdown">
      <div class="planner-select-dropdown-inner">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="planner-item"
          :class="{ cur: bdId === item.bd_id }"
          @click="updatePlanner(item.bd_id)"
        >
          <div class="planner-title">{{ item.title }}</div>
          <div class="planner-tags">
            <div
              v-for="(iitem, iindex) in item.tags"
              :key="iindex"
              class="planner-tags-item"
              >{{ iitem }}</div
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@src/assets/scss/components/planner-select.scss';

import { vOnClickOutside } from '@vueuse/components';
import { computed, ref } from 'vue';
import { recommendBDReport } from '@src/model/bd-report';
import { useBd } from '@src/composable/bd';
import { useUserStats } from '@src/composable/userInfo';
import { reportEvent } from '@src/composable/report';
import { PAGE_NAME } from '@src/model/common';

const props = defineProps<{
  list: any;
  bdId: string;
}>();

const emits = defineEmits(['update:bdId']);
const { getCurrentBdItem } = useBd();
const { character, gameVersion } = useUserStats();
const currentItem = computed(() => getCurrentBdItem(props.bdId));
const showDropdown = ref(false);

const PLACEHOLDER_TEXT = '请先选择适合你的方案';
const plannerTitle = computed(() => {
  return !!props.bdId && currentItem.value
    ? currentItem.value.title
    : PLACEHOLDER_TEXT;
});

const hideDropdownPop = () => {
  showDropdown.value = false;
};

const updatePlanner = (bdId: string) => {
  // 上报BD方案
  recommendBDReport(bdId, character.value, gameVersion.value);
  reportEvent({
    pageName: PAGE_NAME,
    block: 'BD_select',
    action: 'click',
    ext: bdId
  });
  hideDropdownPop();
  emits('update:bdId', bdId);
};
</script>
