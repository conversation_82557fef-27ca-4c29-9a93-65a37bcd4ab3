<template>
  <div
    ref="draggableRef"
    class="debug-bar"
    :style="{ left: x + 'px', top: y + 'px' }"
  >
    <div class="header">
      <p>POE2 In-Game 调试工具</p>
    </div>
    <div class="content">
      <!-- 游戏场景事件 -->
      <div class="section">
        <h3>游戏场景事件</h3>

        <div class="input-group">
          <h4>首次创建角色</h4>
          <button @click="mockFirstCreateActor">触发首次创建角色</button>
        </div>

        <div class="input-group">
          <h4>选择职业</h4>
          <div class="actor-inputs">
            <select v-model="selectedRole">
              <option
                v-for="(className, classId) in classNameMap"
                :key="classId"
                :value="classId"
              >
                {{ className }}
              </option>
            </select>
          </div>
          <button @click="mockSelectRole">触发选择职业</button>
        </div>

        <div class="input-group">
          <h4>升华场景</h4>
          <button @click="mockAdvance">触发升华场景</button>
        </div>

        <div class="input-group">
          <h4>进入异界玩法</h4>
          <button @click="mockEnterEndgame">触发进入异界</button>
        </div>

        <div class="input-group">
          <h4>boss三次战斗结束</h4>
          <div class="actor-inputs">
            <select v-model="selectedBoss">
              <option
                v-for="(bossName, bossId) in bossList"
                :key="bossId"
                :value="bossId"
              >
                {{ bossName }}
              </option>
            </select>
          </div>
          <button @click="mockBossFightEnd">触发boss三次战斗结束</button>
        </div>
      </div>

      <!-- 装备面板 -->
      <div class="section">
        <h3>装备面板</h3>
        <div class="panel-controls">
          <button class="btn-open" @click="mockOpenEquipment">打开</button>
          <button class="btn-close" @click="mockCloseEquipment">关闭</button>
          <button class="btn-f1" @click="mockF1KeyPress('inventory_panel')"
            >触发F1</button
          >
        </div>
      </div>

      <!-- 技能面板 -->
      <div class="section">
        <h3>技能面板</h3>
        <div class="panel-controls">
          <button class="btn-open" @click="mockOpenSkill">打开</button>
          <button class="btn-close" @click="mockCloseSkill">关闭</button>
          <button class="btn-f1" @click="mockF1KeyPress('gem_skill_panel')"
            >触发F1</button
          >
        </div>
      </div>

      <!-- 天赋面板 -->
      <div class="section">
        <h3>天赋面板</h3>
        <div class="panel-controls">
          <button class="btn-open" @click="mockOpenPassiveTree">打开</button>
          <button class="btn-close" @click="mockClosePassiveTree">关闭</button>
          <button class="btn-f1" @click="mockF1KeyPress('passive_skill_panel')"
            >触发F1</button
          >
        </div>
      </div>

      <!-- 系统事件 -->
      <div class="section">
        <h3>系统事件</h3>
        <div class="input-group">
          <h4>窗口分辨率</h4>
          <div class="resolution-inputs">
            <input v-model.number="width" type="number" placeholder="宽度" />
            <span>x</span>
            <input v-model.number="height" type="number" placeholder="高度" />
          </div>
          <button @click="mockWindowResolutionChange">模拟分辨率变更</button>
        </div>
        <div class="input-group">
          <h4>用户信息</h4>
          <button @click="mockUserInfo">获取用户信息</button>
        </div>
      </div>

      <!-- 调试工具 -->
      <div class="section">
        <h3>调试工具</h3>
        <div class="input-group">
          <div class="warning-tip">
            <p>⚠️ 提示：部分场景事件仅首次触发有效</p>
            <p>包括：欢迎页、装备面板、技能面板、天赋面板</p>
            <p>如需重复测试，请先清除 localStorage 数据</p>
          </div>
          <button class="btn-warning" @click="clearFirstTimeRecords">
            清除首次触发记录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useDraggable } from '@vueuse/core';
import { emitter } from '@src/util/event';
import { SCENE_ENTRY_RECORD_KEY } from '@src/model/common';

const draggableRef = ref<HTMLElement | null>(null);
const { x, y } = useDraggable(draggableRef, {
  initialValue: {
    x: window.innerWidth - 380,
    y: 20
  }
});

// 首次创建角色模拟
const mockFirstCreateActor = () => {
  const eventData = {
    event_type: 'GamePlay',
    character_creation: {
      selected_class: 12,
      selected_class_name: ''
    }
  };
  emitter.emit('Msg_POE2InGame_NoticeGameMsg', eventData);
};

// 职业名称映射
const selectedRole = ref('1');
const classNameMap: Record<string, string> = {
  '1': '女巫',
  '2': '游侠',
  '6': '战士',
  '7': '魔巫',
  '9': '佣兵',
  '10': '行者'
};

const mockSelectRole = () => {
  if (!selectedRole.value) return;
  const eventData = {
    event_type: 'GamePlay',
    character_creation: {
      selected_class: Number(selectedRole.value) || null,
      selected_class_name: classNameMap[selectedRole.value]
    }
  };
  emitter.emit('Msg_POE2InGame_NoticeGameMsg', eventData);
};

// 升华场景模拟
const mockAdvance = () => {
  const eventData = {};
  emitter.emit('Msg_POE2InGame_ADCVANCE', eventData);
};

// 进入异界玩法模拟
const mockEnterEndgame = () => {
  const eventData = {};
  emitter.emit('Msg_POE2InGame_NoticeEnterEndgame', eventData);
};

// boss 名称和对应的 ID 映射
const bossList = {
  'Metadata/Monsters/SwollenMiller/SwollenMillerBoss': '黑炎之核-马洛尼',
  'Metadata/Monsters/SwollenMiller/VoidWeaverBoss': '虚空编织者-赛勒斯',
  'Metadata/Monsters/SwollenMiller/EarthbreakerBoss': '裂地者-卡鲁',
  'Metadata/Monsters/SwollenMiller/ShadowWalkerBoss': '暗影行者-莫里斯',
  'Metadata/Monsters/SwollenMiller/StormBringerBoss': '风暴使者-艾莉',
  'Metadata/Monsters/SwollenMiller/FrostGuardianBoss': '霜寒守护者-托尔'
};

// boss 三次战斗结束模拟
const selectedBoss = ref('Metadata/Monsters/SwollenMiller/SwollenMillerBoss');
const mockBossFightEnd = () => {
  emitter.emit('Msg_POE2InGame_NoticeBossStrategy', selectedBoss.value);
};

// 通用面板控制函数
const mockPanelAction = (panelType: string, isOpen: boolean) => {
  const eventData = {
    event_type: 'Character',
    character_name: 'polarisliu',
    ui_states: { [panelType]: isOpen }
  };
  emitter.emit('Msg_POE2InGame_NoticeGameMsg', eventData);
};

// 装备面板控制
const mockOpenEquipment = () => mockPanelAction('inventory_panel', true);
const mockCloseEquipment = () => mockPanelAction('inventory_panel', false);

// 技能面板控制
const mockOpenSkill = () => mockPanelAction('gem_skill_panel', true);
const mockCloseSkill = () => mockPanelAction('gem_skill_panel', false);

// 天赋面板控制
const mockOpenPassiveTree = () => mockPanelAction('passive_skill_panel', true);
const mockClosePassiveTree = () =>
  mockPanelAction('passive_skill_panel', false);

// 窗口分辨率变更模拟
const width = ref(1920);
const height = ref(1080);
const mockWindowResolutionChange = () => {
  const eventData = { width: width.value, height: height.value };
  emitter.emit('Msg_POE2InGame_ResolutionChange', eventData);
};

// F1按键事件模拟
const mockF1KeyPress = (panel: string) => {
  emitter.emit('Msg_POE2InGame_SwitchHelperPanel', panel);
};

// 发送用户信息数据
const userInfo = {
  event_type: 'StartGame',
  character_name: 'polaris',
  all_stats: {
    armour: {
      armour: '82',
      'display_estimated_physical_damage_reduction_%': '25%'
    },
    character: {
      ascendancy: '佣兵',
      level: '13',
      class: '1'
    }
  },
  all_passives: {
    passives: [
      {
        id: 'armour_and_evasion2',
        name: '护甲和闪避值',
        type: 0
      },
      {
        id: 'attack_speed2_',
        name: '攻击速度',
        type: 0
      },
      {
        id: 'cooldowns1',
        name: '冷却回复速度',
        type: 0
      }
    ],
    atlas_passives: [],
    jewels: []
  }
};
const mockUserInfo = () => {
  emitter.emit('Msg_POE2InGame_NoticeGameMsg', userInfo);
};

// 清除首次触发记录
const clearFirstTimeRecords = () => {
  try {
    localStorage.removeItem(SCENE_ENTRY_RECORD_KEY);
    alert('✅ 首次触发记录已清除！\n现在可以重新测试首次触发的场景事件了。');
  } catch (error) {
    alert(`❌ 清除失败：${error}`);
  }
};
</script>

<style scoped>
.debug-bar {
  position: fixed;
  z-index: 9999;
  width: 360px;
  max-height: 85vh;
  overflow-y: auto;
  background-color: #1a1d26;
  border: 1px solid #2a2d3a;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.6);
  color: #e0e0e0;
  font-family: Arial, sans-serif;
}

.header {
  background-color: #0f1117;
  padding: 10px 14px;
  cursor: move;
  user-select: none;
  border-bottom: 1px solid #2a2d3a;
  position: sticky;
  top: 0;
  z-index: 1;
}

.header p {
  margin: 0;
  font-weight: bold;
  font-size: 14px;
}

.content {
  padding: 14px;
}

.section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #2a2d3a;
}

.section:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.section h3 {
  margin: 0 0 12px 0;
  font-size: 15px;
  color: #4a9eff;
  font-weight: 600;
  border-left: 3px solid #4a9eff;
  padding-left: 8px;
}

.input-group {
  margin-bottom: 12px;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-group h4 {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: #b0b0b0;
  font-weight: 500;
}

.panel-controls {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

input,
textarea,
select {
  width: 100%;
  padding: 8px 10px;
  border-radius: 4px;
  border: 1px solid #2a2d3a;
  background-color: #0f1117;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 13px;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #4a4d5a;
}

.actor-inputs {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.actor-inputs input,
.actor-inputs select {
  flex: 1;
}

.resolution-inputs {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.resolution-inputs input {
  width: 80px;
}

.resolution-inputs span {
  color: #6a6d7a;
}

button {
  width: 100%;
  padding: 8px 12px;
  background-color: #2d4a7c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  transition: background-color 0.2s;
}

.btn-open {
  background-color: #2d7c4a;
}

.btn-close {
  background-color: #7c2d2d;
}

.btn-f1 {
  background-color: #7c7c2d;
}

button:hover {
  filter: brightness(1.2);
}

button:active {
  filter: brightness(0.8);
}

.warning-tip {
  background-color: #2a1f1a;
  border: 1px solid #7c5d2d;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.warning-tip p {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #e6c77a;
  line-height: 1.4;
}

.warning-tip p:last-child {
  margin-bottom: 0;
}

.btn-warning {
  background-color: #7c5d2d !important;
}

.btn-warning:hover {
  background-color: #8a6a35 !important;
}
</style>
