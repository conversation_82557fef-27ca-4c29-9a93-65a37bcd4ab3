.bd-panel {
  width: 5.2rem;
  height: 7.36rem;
  background: url("@src/assets/images/win/bd-panel-main.png") no-repeat;
  background-size: 100% calc(100% - 3.51rem - 1.45rem);
  background-position-y: 3.51rem;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 100%;
    height: 3.52rem;
    background: url("@src/assets/images/win/bd-panel-before.png");
    background-size: 100% 100%;
    z-index: -1;
  }

  &::after {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1.46rem;
    background: url("@src/assets/images/win/bd-panel-after.png");
    background-size: 100% 100%;
    z-index: -1;
  }

  .planner-select-wrap {
    // height: 0.72rem;
    margin: 0.24rem 0;
    display: flex;
    align-items: center;
  }
}


.bd-panel-close {
  position: absolute;
  right: 0;
  top: 0.1rem;
  width: 0.74rem;
  height: 0.44rem;
  background: url("@src/assets/images/ui/chat-icon-close.png") no-repeat;
  background-size: contain;
  cursor: pointer;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("@src/assets/images/ui/chat-icon-close-hover.png") no-repeat;
    background-size: 100% 100%;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.2s linear;
  }

  &:hover {
    &::after {
      opacity: 1;
    }
  }
}