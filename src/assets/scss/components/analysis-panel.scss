.analysis-panel {
  width: 5.2rem;
  height: 3.55rem;
  background: url("@src/assets/images/win/analysis-panel-main.png") no-repeat;
  // background-position: 0 3.52rem;
  background-size: 100% calc(100% - 1.47rem - 1.04rem);
  background-position-y: 1.47rem;
  position: relative;
  z-index: 10;
  padding-top: 0.26rem;
  padding-bottom: 0.2rem;
  display: flex;
  flex-direction: column;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 100%;
    height: 1.48rem;
    background: url("@src/assets/images/win/analysis-panel-before.png");
    background-size: 100% 100%;
    z-index: -1;
  }

  &::after {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1.05rem;
    background: url("@src/assets/images/win/analysis-panel-after.png");
    background-size: 100% 100%;
    z-index: -1;
  }

}

.analysis-hd {
  height: 0.44rem;
  padding: 0 0.32rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.analysis-title-wrap {
  display: inline-flex;
  align-items: center;
}

.analysis-title {
  width: 1.22rem;
  height: 0.31rem;
  background: url("@src/assets/images/ui/analysis-title.png") no-repeat;
  background-size: contain;
}

.analysis-subtitle {
  width: 0.76rem;
  height: 0.27rem;
  margin-left: 0.04rem;
  margin-bottom: -0.04rem;
  position: relative;
  cursor: pointer;
  &::before,
  &::after{
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    transition: opacity 0.15s linear;
  }
  &::before{
    background: url("@src/assets/images/ui/analysis-subtitle.png") no-repeat;
    background-size: contain;
    opacity: 1;
  }
  &::after{
    background: url("@src/assets/images/ui/analysis-subtitle-hover.png") no-repeat;
    background-size: contain;
    opacity: 0;
  }

  &:hover {
    &::before {
      opacity: 0;
    }
    &::after {
      opacity: 1;
    }
  }
}

.analysis-desc {
  color: rgba(197, 193, 189, 0.48);
  text-shadow: 0px 0px 4px #000;
  font-size: 0.12rem;
}

.analysis-bd {
  // width: 100%;
  height: 100%;
  flex: 1;
  overflow: hidden;
  overflow-y: auto;
  margin: 0.1rem 0;
  margin-right: 0.12rem;
}

.analysis-markdown.bd-analysis-markdown {
  margin: 0.1rem 0;
  padding: 0 0.2rem 0 0.36rem;
  color: rgba(206, 205, 203, 0.8) !important;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  .h6 {
    color: rgba(206, 205, 203, 0.8) !important;
  }

  h2:nth-child(1) {
    margin-bottom: 0.16rem;
    font-size: 0.18rem;
  }

  h2+h3 {
    margin-top: 0;
  }

  h3 {
    font-size: 0.14rem;
    line-height: 0.22rem;
    // font-weight: bold;
    // color: rgba(197, 193, 189, 0.80);
    // margin: 0.12rem 0 0.06rem;
    display: inline-flex;
    align-items: center;
    margin-top: 0.16rem;
    margin-bottom: 0.06rem;

    &::before {
      content: "";
      display: block;
      width: 0.16rem;
      height: 0.16rem;
      background: url("@src/assets/images/ui/analysis-tit.png");
      margin-right: 0.06rem;
      background-size: contain;
      flex-shrink: 0;
    }
  }

  p {
    margin: 0;
    font-size: 0.12rem;
    line-height: 0.18rem;
  }
}