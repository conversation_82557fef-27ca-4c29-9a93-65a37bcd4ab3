.planner-select-wrap {
  position: relative;
}

.planner-select {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  height: 0.29rem;
  width: 100%;
  overflow: hidden;
  &:hover {
    .planner-title{
      background: linear-gradient(to bottom, #FFDE69 0%, #FFE6A8 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .planner-select-icon::after {
      opacity: 1;
    }
  }

  .planner-title {
    font-size: 0.2rem;
    line-height: 0.28rem;
    background: linear-gradient(to bottom, #D3C182 0%, #C5AA69 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    flex: 1;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all 0.2s ease-in-out;
  }

  .planner-tags {
    column-gap: 0.04rem;
    margin-left: 0.06rem;
  }

  .planner-tags-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    height: 0.24rem;
    padding: 0.04rem 0;
    background: url("@src/assets/images/ui/tag-main.png");
    background-size: 100% 100%;
    position: relative;
    font-size: 0.12rem;
    color: rgba(147, 140, 111, 0.8);
    font-weight: bold;

    &::before {
      content: "";
      display: block;
      width: 0.06rem;
      height: 0.24rem;
      background: url("@src/assets/images/ui/tag-before.png") no-repeat;
      background-size: 100% 100%;
    }

    &::after {
      content: "";
      display: block;
      width: 0.06rem;
      height: 0.24rem;
      background: url("@src/assets/images/ui/tag-after.png") no-repeat;
      background-size: 100% 100%;
    }
  }
}

.planner-select-icon {
  width: 0.29rem;
  height: 0.29rem;
  margin-left: 0.12rem;
  background: url("@src/assets/images/ui/icon-change.png") no-repeat;
  background-size: contain;
  position: relative;
  cursor: pointer;
  flex-shrink: 0;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0.29rem;
    height: 0.29rem;
    background: url("@src/assets/images/ui/icon-change-hover.png") no-repeat;
    background-size: contain;
    opacity: 0;
    z-index: 1;
    transition: opacity 0.15s linear;
  }

  &:hover {
    &::after {
      opacity: 1;
    }
  }
}

.planner-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.planner-select-dropdown {
  position: absolute;
  top: 0.36rem;
  width: 100%;
  background: #000;
  border: 2px solid #6F604A;
  max-height: 2.68rem;
  overflow: hidden;
  overflow-y: auto;
  z-index: 20;
}

.planner-title {
  width: 100%;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  &::after {
    content: attr(data-name);
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    text-shadow: 0px 2px 5px rgba(0, 0, 0, 1);
    z-index: -1;
  }
}

.planner-tags {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  min-width: 0.6rem;
}

.planner-select-dropdown-inner {
  padding: 0.08rem 0;
}

.planner-select-dropdown {
  .planner-item {
    padding: 0 0.16rem;
    height: 0.24rem;
    margin-bottom: 0.04rem;
    cursor: pointer;

    &:last-child {
      margin-bottom: 0;
    }

    &.cur,
    &:hover {
      background: #6F604A;

      .planner-title,
      .planner-tags-item {
        color: #000;
      }
    }
  }

  .planner-title {
    font-size: 0.12rem;
    color: #6F604A;
  }

  .planner-tags-item {
    font-size: 0.12rem;
    color: #717171;
    display: inline-flex;

    &::after {
      content: "|";
      display: block;
    }

    &:last-child {
      &::after {
        content: none;
      }
    }
  }
}