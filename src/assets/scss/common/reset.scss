* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  appearance: none;
  outline: none;
}
html,
body {
  user-select: none;
  cursor: default;
}
body {
  overflow: hidden;
}
img {
  -webkit-user-drag: none;
  user-drag: none;
}
button,
input,
textarea,
select {
  font-size: 12px;
  line-height: 1.5;
  font-family: var(--ui-font-family-system);
  color: var(--ui-color-text-1);
  font-weight: 400;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
}

body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
li,
button,
input {
  margin: 0;
  padding: 0;
  word-wrap: break-word;
  word-break: break-word;
}

em,
i,
b {
  font-style: normal;
}

a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
}

ul,
ol,
li {
  list-style: none;
}

img {
  border: 0;
  font-size: 0;
  user-select: none;
  vertical-align: middle;
}

select,
button {
  user-select: none;
}

::-webkit-scrollbar {
  height: 0.06rem;
  width: 0.06rem;
  cursor: pointer;
}

::-webkit-resizer,
::-webkit-scrollbar {
  background: transparent;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  border: 0.016rem solid #2a2825;
  background: url("@src/assets/images/win/win-scrollbar.png") repeat 0 0;
  border-radius: 0;
}

::-webkit-scrollbar-thumb:hover {
  border: 0 solid #2a2825;
  background: url("@src/assets/images/win/win-scrollbar-hover.png") repeat 0 0;
}
