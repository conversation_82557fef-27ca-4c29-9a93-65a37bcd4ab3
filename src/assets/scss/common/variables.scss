// @font-face {
//   font-family: 'CARegular';
//   src: url('@src/assets/fonts/font-regular.otf') format('truetype');
// }

// @font-face {
//   font-family: 'CAExtrabold';
//   src: url('@src/assets/fonts/font-extrabold.otf') format('truetype');
// }

:root {
  // 系统字体
  --ui-font-family-system: system-ui, "calibri", "Roboto", verdana,
    "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei",
    sans-serif;

  //数字字体
  --ui-font-family-number: "CARegular", system-ui, "calibri", "Roboto", verdana,
    "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei",
    sans-serif;

  // 品牌色
  --ui-color-brand1: #a89565;

  // 文本色
  --ui-color-text-1: #C5C1BD;
  --ui-color-text-2: rgba(197, 193, 189, 0.8);

  // 滚动条样式
  --ui-scrollbar-default: rgba(255, 255, 255, 0.25);
  --ui-scrollbar-hover: rgba(255, 255, 255, 0.5);

  // 强调色
  --ui-color-highlight: #cd793c;
}
