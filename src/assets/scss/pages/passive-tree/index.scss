html {
  font-size: calc(100vw / 1920) * 100;
  height: 100%;
}


.win-client-entry {
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 3.54rem;
  aspect-ratio: 354 / 53;
  cursor: pointer;
  pointer-events: all;

  &::before,
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    transition: opacity 0.2s linear;
  }
  

  &::before {
    opacity: 1;
  }

  &::after {
    opacity: 0;
  }

  &:hover {
    &::after {
      opacity: 1;
    }
  }
  &.tree{
    left: 2.75rem;
    &::before {
      background: url('@src/assets/images/win/win-entry-tree.png') no-repeat 50% 0;
      background-size: contain;
    }
  
    &::after {
      background: url('@src/assets/images/win/win-entry-tree-hover.png') no-repeat 50% 0;
      background-size: contain;
    }
  }
}

.page-frame--tree {
  position: fixed;
  inset: 0;
  left: 2.3rem;
  top: 0;
  bottom: 20px;
  z-index: 100;
}

.bd-panel.tree-panel {
  width: 4.5rem;
  height: 70%;
  background: url("@src/assets/images/win/tree-panel-main.png") no-repeat;
  background-size: 100% calc(100% - 3.51rem - 1.45rem);
  background-position-y: 3.51rem;
  position: relative;

  &::before {
    background: url("@src/assets/images/win/tree-panel-before.png");
    background-size: 100% 100%;
  }

  &::after {
    background: url("@src/assets/images/win/tree-panel-after.png");
    background-size: 100% 100%;
  }
}

.analysis-panel.tree-analysis-panel {
  width: 4.5rem;
  height: calc(30% + 0.28rem);
  margin-top: -0.28rem;
}


.tree-panel-hd {
  width: 100%;
  height: 0.48rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tree-panel-bd {
  height: calc(100% - 0.48rem);
  padding: 0.1rem 0.3rem 0.16rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.tree-panel-tit {
  width: 2.17rem;
  height: 0.31rem;
  background: url("@src/assets/images/tree/tree-panel-title.png") no-repeat;
  background-size: contain;
  margin-top: 0.1rem;
}

.tree-planner-main {
  width: 3.88rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-top: 0.1rem;
  padding-bottom: 0.14rem;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 0.07rem;
    background: url("@src/assets/images/tree/tree-main-line.png") no-repeat;
    background-size: 100% 100%;
  }
}

.tree-main-tit {
  width: 100%;
  height: 0.36rem;
  background: url("@src/assets/images/tree/tree-main-tit.png") no-repeat;
  background-size: 100% 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.tree-main-tit-item {
  color: rgba(197, 193, 189, 0.60);
  text-shadow: 0px 0px 6px #000;
  font-size: 0.14rem;
  margin-right: 0.2rem;
  display: inline-flex;
  align-items: center;

  span {
    color: #CCB675;
    font-weight: bold;
    font-size: 0.18rem;
    margin-left: 0.04rem;
  }

  &:last-child {
    margin-right: 0;
  }
}

.tree-main-legend {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.06rem;
}

.tree-main-legend-item {
  color: rgba(197, 193, 189, 0.60);
  text-shadow: 0px 0px 6px #000;
  font-size: 0.12rem;
  line-height: 0.18rem;
  margin-right: 0.08rem;
  display: inline-flex;
  align-items: center;

  &:last-child {
    margin-right: 0;
  }

  &::before {
    content: "";
    display: block;
    width: 0.12rem;
    height: 0.12rem;
    margin-right: 0.02rem;
  }

  &.done::before {
    background: url("@src/assets/images/tree/icon-yellow.png") no-repeat;
    background-size: contain;
  }

  &.recommend::before {
    background: url("@src/assets/images/tree/icon-blue.png") no-repeat;
    background-size: contain;
  }

  &.weapon-done::before {
    background: url("@src/assets/images/tree/icon-green.png") no-repeat;
    background-size: contain;
  }

  &.weapon-recommend::before {
    background: url("@src/assets/images/tree/icon-purple.png") no-repeat;
    background-size: contain;
  }
}

.tree-planner-detail {
  margin-top: 0.16rem;
  margin-right: -0.16rem;
  padding-right: 0.2rem;
  overflow: hidden;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.tree-planner-detail-inner.analysis-markdown {

  h3 {
    color: rgba(210, 195, 140, 0.80);
    text-shadow: 0px 0px 6px #000;
    margin-top: 0.16rem;
    margin-bottom: 0.1rem;

    &:nth-child(1) {
      margin-top: 0;
    }
  }

  li strong {
    display: inline-flex;
    align-items: center;

    &::before {
      content: "";
      display: block;
      width: 0.1rem;
      height: 0.1rem;
      background: url("@src/assets/images/tree/icon-arrow.png") no-repeat;
      background-size: contain;
      margin: 0 0.08rem;
    }
  }
}

.tree-recommend-dialog {
  position: absolute;
  width: 3.61rem;
  top: 0;
  padding: 0.36rem;
  background: url("@src/assets/images/tree/pop-main.png") no-repeat;
  // background-position: 0 3.52rem;
  background-size: 100% calc(100% - 0.5rem - 0.5rem);
  background-position-y: 0.5rem;
  z-index: 11;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 100%;
    height: 0.5rem;
    background: url("@src/assets/images/tree/pop-before.png");
    background-size: 100% 100%;
    z-index: -1;
  }

  &::after {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0.5rem;
    background: url("@src/assets/images/tree/pop-after.png");
    background-size: 100% 100%;
    z-index: -1;
  }
}

.tree-dialog-bd {
  color: rgba(197, 193, 189, 0.80);
  font-size: 0.14rem;
  line-height: 0.22rem;
}

.tree-dialog-ft {
  display: inline-flex;
  align-items: center;
  margin-top: 0.2rem;

  .tree-dialog-btn {
    margin-right: 0.08rem;

    &:last-child {
      margin-right: 0;
    }
  }
}

.tree-dialog-btn {
  min-width: 1.42rem;
  height: 0.36rem;
  font-size: 0.14rem;
  background: url("@src/assets/images/tree/btn-default.png");
  background-size: 100% 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #C0BEB5;
  font-weight: bold;
  cursor: pointer;

  &::after {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: url("@src/assets/images/tree/btn-default-hover.png");
    background-size: 100% 100%;
    opacity: 0;
    transition: opacity 0.2s linear;
  }

  span {
    z-index: 1;
    transition: all 0.2s ease-in-out;
  }

  &:hover {
    &::after {
      opacity: 1;
    }

    span {
      filter: brightness(1.2);
    }
  }

  &.default {
    background: url("@src/assets/images/tree/btn-default.png");
    background-size: 100% 100%;

    &::after {
      background: url("@src/assets/images/tree/btn-default-hover.png");
      background-size: 100% 100%;
    }
  }

  &.primary {
    background: url("@src/assets/images/tree/btn-primary.png");
    background-size: 100% 100%;
    color: #D2C38C;

    &::after {
      background: url("@src/assets/images/tree/btn-primary-hover.png");
      background-size: 100% 100%;
    }
  }
}