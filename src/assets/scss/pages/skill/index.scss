html {
  font-size: calc(100vw / 1920) * 100;
  height: 100%;
}

.win-client-entry {
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 3.54rem;
  aspect-ratio: 354 / 53;
  cursor: pointer;
  pointer-events: all;

  &::before,
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    transition: opacity 0.2s linear;
  }
  

  &::before {
    opacity: 1;
  }

  &::after {
    opacity: 0;
  }

  &:hover {
    &::after {
      opacity: 1;
    }
  }
  &.skill{
    left: 7.5rem;
    &::before {
      background: url('@src/assets/images/win/win-entry-skill.png') no-repeat 50% 0;
      background-size: contain;
    }
  
    &::after {
      background: url('@src/assets/images/win/win-entry-skill-hover.png') no-repeat 50% 0;
      background-size: contain;
    }
  }
}

.page-frame--skill {
  position: fixed;
  inset: 0;
  left: 6.68rem;
  top: 0;
  bottom: 20px;
  z-index: 100;
}

.bd-panel.skill-panel {
  height: 70%;
}

.analysis-panel.skill-analysis-panel {
  height: calc(30% + 0.28rem);
  margin-top: -0.28rem;
}


.skill-panel-hd {
  width: 100%;
  height: 0.48rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.skill-panel-bd {
  height: calc(100% - 0.48rem);
  padding: 0.1rem 0.3rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.skill-panel-tit {
  width: 2.17rem;
  height: 0.31rem;
  background: url("@src/assets/images/skill/skill-panel-title.png") no-repeat;
  background-size: contain;
  margin-top: 0.1rem;
}

.skill-planner-main {
  width: 4.8rem;
  // height: 1.64rem;
  background: url("@src/assets/images/skill/detail-item-bg.png") no-repeat;
  background-size: 100% 100%;
  padding: 0.13rem 0.13rem 0.23rem 0.33rem;
  margin-left: -0.11rem;
  margin-top: 0.04rem;
}

.planner-author {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.author-info {
  width: 100%;
  overflow: hidden;
}

.author-avatar {
  width: 0.82rem;
  height: 0.64rem;
  position: relative;
  flex-shrink: 0;
  margin-right: 0.16rem;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("@src/assets/images/skill/author-avatar.png") no-repeat;
    background-size: 100% 100%;
    z-index: 1;
  }
}

.author-main {
  width: 100%;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
}

.author-name {
  color: #C5AA69;
  font-size: 0.18rem;
  font-weight: bold;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.author-channel-icon {
  width: 0.27rem;
  height: 0.27rem;
  margin-left: 0.04rem;
  flex-shrink: 0;

  &.bilibili {
    background: url("@src/assets/images/skill/channel-bilibili.png") no-repeat;
    background-size: contain;
  }

  &.caimogu {
    background: url("@src/assets/images/skill/channel-caimogu.png") no-repeat;
    background-size: contain;
  }

  &.tiktok {
    background: url("@src/assets/images/skill/channel-tiktok.png") no-repeat;
    background-size: contain;
  }
}

.planner-time {
  color: rgba(197, 193, 189, 0.40);
  text-shadow: 0px 0px 4px #000;
  font-size: 0.12rem;
  margin-top: 0.06rem;

  span {
    display: inline-flex;
    align-items: center;

    &::after {
      content: "|";
      color: rgba(255, 255, 255, 0.10);
      margin: 0 0.09rem;
    }

    &:last-child {
      &::after {
        content: none;
      }
    }
  }
}

.planner-skill-key {
  margin-top: 0.18rem;
}

.skill-key-item {
  display: inline-flex;
  align-items: flex-start;
  margin-bottom: 0.09rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.skill-key-tit {
  width: 0.96rem;
  color: #ECE1B6;
  font-size: 0.14rem;
  flex-shrink: 0;
}

.skill-key-progress {
  color: rgba(197, 193, 189, 0.80);
  text-shadow: 0px 0px 4px #000;
  font-size: 0.12rem;

  span {
    display: inline-flex;
    align-items: center;

    &::after {
      content: "";
      display: block;
      width: 0.07rem;
      height: 0.1rem;
      margin: 0 0.06rem;
      background: url("@src/assets/images/skill/progress-arrow.png") no-repeat;
      background-size: contain;
    }

    &:last-child::after {
      content: none;
    }

  }
}

.skill-planner-detail {
  flex: 1;
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
  padding-right: 0.14rem;
  margin-right: -0.16rem;
  margin-top: 0.1rem;
  padding-bottom: 0.1rem;
}

.skill-detail-item {
  width: 100%;
  height: fit-content;
  background: url("@src/assets/images/skill/skill-detail-bg.jpg") no-repeat;
  background-size: 100% 100%;
  padding: 0.12rem 0.25rem 0.14rem;
  margin-top: 0.1rem;

  &:first-child {
    margin-top: 0;
  }
}

.skill-detail-main {
  display: flex;
  align-items: center;
}

.skill-detail-main-icon {
  width: 0.44rem;
  height: 0.44rem;
  position: relative;
  margin-right: 0.12rem;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("@src/assets/images/skill/skill-icon.png") no-repeat;
    background-size: 100% 100%;
    z-index: 1;
  }
}

.skill-detail-main-tit {
  color: rgba(206, 205, 203, 0.80);
  text-shadow: 0px 0px 4px #000;
  font-size: 0.16rem;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.skill-detail-gems {
  display: flex;
  align-items: center;
  margin-top: 0.06rem;
}

.skill-detail-gem {
  width: 0.44rem;
  height: 0.44rem;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.skill-detail-gem-chain {
  display: inline-flex;
  align-items: center;
  width: 100%;
  flex: 1;
  padding-left: 0.28rem;
}

.skill-detail-subgem {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-icon {
    width: 0.32rem;
    height: 0.32rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  &-tit {
    color: rgba(197, 193, 189, 0.80);
    text-align: center;
    text-shadow: 0px 0px 4px #000;
    margin-top: 0.04rem;
    font-size: 0.12rem;
  }
}


.gem-popover {
  position: absolute;
  width: max-content;
  min-width: 2.2rem;
  padding: 0.16rem;
  background: #1C1814;
  box-shadow: 0px 0.04rem 0.12rem 0px rgba(0, 0, 0, 0.50);
  z-index: 110;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0.6rem;
    background: url("@src/assets/images/skill/gem-popover-deco.png") no-repeat;
    background-size: 100% 100%;
  }
}

.gem-main {
  display: flex;
  align-items: center;
}

.gem-icon {
  width: 0.48rem;
  height: 0.48rem;
  object-fit: contain;
  flex-shrink: 0;
  margin-right: 0.12rem;
}

.gem-info {
  width: 100%;
  flex: 1;
}

.gem-tit {
  color: #CCB675;
  font-size: 0.16rem;
  font-weight: bold;
}

.gem-text {
  color: #C5C1BD;
  font-size: 0.14rem;
  margin-top: 0.02rem;
}

.gem-desc {
  color: #D2C38C;
  font-size: 0.14rem;
  width: 100%;
  min-height: 0.28rem;
  padding: 0.05rem;
  margin-top: 0.12rem;
  text-align: center;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.00) 100%);
}

.gem-skill {
  margin-top: 0.14rem;
}

.gem-skill-text {
  color: rgba(197, 193, 189, 0.80);
  font-size: 0.14rem;
  margin-bottom: 0.12rem;

  &:last-child {
    margin-bottom: 0;
  }

  span {
    color: rgba(210, 195, 140, 0.80);
  }
}
