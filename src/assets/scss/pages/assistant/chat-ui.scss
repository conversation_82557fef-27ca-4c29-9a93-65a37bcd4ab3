.section-chat {
  position: absolute;
  top: 0.4rem;
  left: 0.8rem;
  z-index: 10;
  font-size: 0.14rem;
  font-weight: 300;
}
.chat-dialog {
  width: 10.6rem;
  position: relative;
  padding: 0.1rem 0.1rem 0.3rem 0.1rem;
  will-change: width;
}

.chat-cont {
  position: relative;
  z-index: 10;
}
.chat-cont-title {
  width: 100%;
  height: 0.72rem;
  background: url('@src/assets/images/win/win-chat-title-bg.jpg') repeat-x 0 0;
  background-size: 10.88rem 0.72rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.16rem 0 0.12rem;
}
.window-title-logo {
  width: 2.76rem;
  aspect-ratio: 276 / 87;
  background: url('@src/assets/images/ui/title-ddj-bg.png') no-repeat 0 0;
  background-size: contain;
  display: block;
}
.window-title-desc {
  font-size: 0.12rem;
  color: rgba(197, 193, 189, 0.4);
  display: none;
}
.chat-cont-inner {
  height: 6rem;
  display: flex;
}
.chat-main {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 5;
  margin-left: -1rem;
}
.chat-action-tools {
  padding: 0.05rem 0.12rem 0.1rem 0.12rem;
  position: relative;
  display: flex;
}
.chat-tools-main {
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  row-gap: 0.08rem;
  column-gap: 0.08rem;
}
.chat-messages {
  height: 100%;
  flex: 1;
  overflow: hidden;
}
.chat-textarea {
  padding: 0rem 0.12rem 0.14rem 0.12rem;
}
.chat-textarea-ele {
  padding: 0.1rem;
  font-size: 0;
  position: relative;
  .textarea-border {
    position: absolute;
    inset: 0;
    border: 1px solid rgba($color: #fff, $alpha: 0.1);
    background: rgba($color: #000, $alpha: 0.6);
    border-radius: 0.04rem;
    z-index: 1;
    transition: border-color 0.2s linear, background-color 0.2s linear;
  }
  textarea {
    width: 100%;
    height: 0.8rem;
    overflow: auto;
    background: transparent;
    appearance: none;
    border: none;
    outline: none;
    position: relative;
    z-index: 3;
    font-size: 0.14rem;
    &::-webkit-scrollbar-thumb {
      border-radius: 200px;
      background-color: #39321f;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: #695d3f;
    }
    &::-webkit-input-placeholder {
      font-size: 0.14rem;
      color: rgb(168, 164, 160, 0.4);
    }
    &:hover,
    &:focus {
      & + .textarea-border {
        border: 1px solid rgba($color: #fff4b4, $alpha: 0.4);
        background: rgba($color: #000, $alpha: 0.7);
      }
    }
  }
}
.chat-textarea-actions {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.chat-button-send {
  width: 0.6rem;
  height: 0.32rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: -0.16rem;
  cursor: pointer;
  &::before,
  &::after {
    content: '';
    width: 0.24rem;
    height: 0.24rem;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -0.12rem 0 0 -0.12rem;
    z-index: 1;
    transition: opacity 0.2s linear;
  }
  &::before {
    content: '';
    background: url('@src/assets/images/ui/icon-input-send.png');
    background-size: contain;
  }
  &::after {
    background: url('@src/assets/images/ui/icon-input-send-hover.png');
    background-size: contain;
    opacity: 0;
  }
  &:hover {
    &::before {
      opacity: 0;
    }
    &::after {
      opacity: 1;
    }
  }
  &:active {
    &::before {
      opacity: 0;
    }
    &::after {
      opacity: 0.75;
    }
  }
}
.chat-tools {
  position: absolute;
  right: -0.1rem;
  top: -0.1rem;
  display: flex;
  z-index: 20;
}
.chat-button-close,
.chat-button-expand,
.chat-button-collapse {
  width: 0.4rem;
  height: 0.44rem;
  display: block;
  cursor: pointer;
  position: relative;
  &::after,
  &::before {
    content: '';
    width: 0.74rem;
    height: 0.44rem;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -0.22rem 0 0 -0.37rem;
    pointer-events: none;
    transition: opacity 0.2s linear;
  }
  &:hover {
    &::before {
      opacity: 0;
    }
    &::after {
      opacity: 1;
    }
  }
  &:active {
    &::before {
      opacity: 0;
    }
    &::after {
      opacity: 0.75;
    }
  }
}
.chat-button-close {
  margin: 0 0.06rem 0 0;
  &::before {
    background: url('@src/assets/images/ui/chat-icon-close.png');
    background-size: 100% 100%;
    z-index: 2;
  }
  &::after {
    background: url('@src/assets/images/ui/chat-icon-close-hover.png');
    background-size: 100% 100%;
    z-index: 2;
    opacity: 0;
  }
}

.chat-button-expand {
  &::before {
    background: url('@src/assets/images/ui/chat-icon-expand.png');
    background-size: 100% 100%;
    z-index: 1;
  }
  &::after {
    background: url('@src/assets/images/ui/chat-icon-expand-hover.png');
    background-size: 100% 100%;
    z-index: 2;
    opacity: 0;
  }
}
.chat-button-collapse {
  &::before {
    background: url('@src/assets/images/ui/chat-icon-collapse.png');
    background-size: 100% 100%;
    z-index: 1;
  }
  &::after {
    background: url('@src/assets/images/ui/chat-icon-collapse-hover.png');
    background-size: 100% 100%;
    z-index: 2;
    opacity: 0;
  }
}
.chat-bg {
  position: absolute;
  inset: 0;
  z-index: 1;
}
.chat-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.4rem;
  background: url('@src/assets/images/win/win-bg-tm.png') repeat-x 0 0;
  background-size: 2.52rem 0.4rem;
  z-index: 5;
  &::before,
  &::after {
    content: '';
    width: 0.7rem;
    height: 1.22rem;
    position: absolute;
    top: 0;
  }
  &::before {
    top: -0.246rem;
    left: -0.2rem;
    background: url('@src/assets/images/win/win-bg-tl.png') no-repeat 0 0;
    background-size: 100% auto;
  }
  &::after {
    top: -0.25rem;
    right: -0.2rem;
    background: url('@src/assets/images/win/win-bg-tr.png') no-repeat 0 0;
    background-size: 100% auto;
  }
}
.chat-bg-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0.6rem;
  background: url('@src/assets/images/win/win-bg-bm.png') repeat-x 0 0;
  background-size: 2.82rem 0.6rem;
  z-index: 5;
  &::before,
  &::after {
    content: '';
    width: 0.6rem;
    height: 0.86rem;
    position: absolute;
    top: 0;
  }
  &::before {
    top: -0.07rem;
    left: -0.2rem;
    background: url('@src/assets/images/win/win-bg-bl.png') no-repeat 0 0;
    background-size: 100% auto;
  }
  &::after {
    top: -0.07rem;
    right: -0.2rem;
    background: url('@src/assets/images/win/win-bg-br.png') no-repeat 0 0;
    background-size: 100% auto;
  }
}
.chat-bg-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 0.38rem;
  bottom: 0.38rem;
  background: url('@src/assets/images/win/win-bg-repeat.png') no-repeat 0 0;
  background-size: 100% 100%;
  z-index: 1;
  &::before,
  &::after {
    content: '';
    width: 0.4rem;
    position: absolute;
    top: 0;
    bottom: 0;
  }
  &::before {
    left: 0;
    background: url('@src/assets/images/win/win-bg-l.png') repeat-y 0 0;
    background-size: 0.4rem 5.2rem;
  }
  &::after {
    right: 0;
    background: url('@src/assets/images/win/win-bg-r.png') repeat-y 0 0;
    background-size: 0.4rem 5.2rem;
  }
}
.chat-bg-top-center {
  width: 1.6rem;
  height: 0.46rem;
  position: absolute;
  top: -0.36rem;
  left: 50%;
  margin-left: -0.8rem;
  background: url('@src/assets/images/win/win-bg-tc.png') no-repeat 50% 0;
  background-size: contain;
}
.chat-bg-bottom-center {
  width: 2.83rem;
  height: 0.79rem;
  position: absolute;
  bottom: -0.185rem;
  left: 50%;
  margin-left: -1.41rem;
  background: url('@src/assets/images/win/win-bg-bc.png') no-repeat 50% 0;
  background-size: contain;
}
.chat-bg-mask {
  position: absolute;
  top: 0.1rem;
  left: 0.1rem;
  right: 0.1rem;
  bottom: 0.3rem;
  background: url('@src/assets/images/win/win-chat-bg-s.jpg') no-repeat 50% 0;
  background-size: 100% 100%;
  z-index: 6;
}
.chat-label-item {
  min-height: 0.24rem;
  min-width: 0.62rem;
  padding: 0.04rem 0.06rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.04rem;
  background: rgba($color: #a8a4a0, $alpha: 0.1);
  color: rgba($color: #d2c38c, $alpha: 0.7);
  font-size: 0.12rem;
  text-shadow: 0 5px 10px #000;
  // margin: 0 0.08rem 0 0;
  cursor: pointer;
  transition: color 0.2s linear, background-color 0.2s linear;
  &:hover {
    background: rgba($color: #a8a4a0, $alpha: 0.2);
    color: rgba($color: #fff4b4, $alpha: 1);
  }
  &:active {
    background: rgba($color: #a8a4a0, $alpha: 0.1);
    color: rgba($color: #d2c38c, $alpha: 0.4);
  }
}

.chat-button-new {
  min-width: 0.62rem;
  height: 0.24rem;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .chat-button-new-text {
    display: block;
    white-space: nowrap;
    padding: 0 0.05rem 0 0.02rem;
    color: rgba($color: #a8a4a0, $alpha: 0.8);
    transition: color 0.2s linear;
  }
  .chat-button-new-icon {
    width: 0.24rem;
    height: 0.24rem;
    display: block;
    position: relative;
    &::before,
    &::after {
      content: '';
      width: 0.24rem;
      height: 0.24rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transition: opacity 0.2s linear;
      margin: -0.12rem 0 0 -0.12rem;
    }
    &::before {
      background: url('@src/assets/images/ui/icon-input-new.png') no-repeat 0 0;
      background-size: contain;
    }
    &::after {
      background: url('@src/assets/images/ui/icon-input-new-hover.png')
        no-repeat 0 0;
      background-size: contain;
      opacity: 0;
    }
  }

  &:hover {
    .chat-button-new-icon::before {
      opacity: 0;
    }
    .chat-button-new-icon::after {
      opacity: 1;
    }
    .chat-button-new-text {
      color: rgba($color: #a8a4a0, $alpha: 1);
    }
  }
  &:active {
    .chat-button-new-icon::before {
      opacity: 0;
    }
    .chat-button-new-icon::after {
      opacity: 0.75;
    }
    .chat-button-new-text {
      color: rgba($color: #a8a4a0, $alpha: 0.8);
    }
  }
}
.chat-category {
  width: 3.28rem;
  height: 100%;
  position: relative;
  z-index: 10;
  pointer-events: none;
  overflow: hidden;
}
.chat-category-box {
  width: 2.28rem;
  height: 100%;
  padding: 0.2rem 0.1rem 0.2rem 0.2rem;
  background: rgba($color: #fff, $alpha: 0.03);
  pointer-events: auto;
  position: relative;
  z-index: 20;
}
.chat-category-button {
  width: 0.16rem;
  height: 0.84rem;
  background: rgba($color: #fff, $alpha: 0.05);
  position: absolute;
  top: 50%;
  margin-top: -0.62rem;
  right: -0.16rem;
  cursor: pointer;
  transition: background-color 0.2s linear;
  clip-path: polygon(0 0, 100% 10px, 100% calc(100% - 10px), 0 100%);
  &::before {
    content: '';
    width: 0.16rem;
    height: 0.16rem;
    display: block;
    background: url('@src/assets/images/ui/icon-arrow-expand-r.png') no-repeat 0
      0;
    background-size: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -0.08rem 0 0 -0.08rem;
    opacity: 0.8;
    transition: opacity 0.2s linear;
  }
  &:hover {
    background: rgba($color: #fff, $alpha: 0.1);
    &::before {
      opacity: 1;
    }
  }
}

.chat-category-inner {
  height: 100%;
  position: relative;
  overflow: scroll;
  overflow-x: hidden;
  padding-right: 0.1rem;
  &::-webkit-scrollbar-thumb {
    background-color: #39321f;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: #695d3f;
  }
}
.chat-group-item {
  padding-bottom: 0.12rem;
}
.chat-group-title {
  font-size: max(0.1rem, 10px);
  color: rgba($color: #cecdcb, $alpha: 0.6);
  padding-bottom: 0.06rem;
  span {
    // zoom: 0.85;
    opacity: 0.8;
    text-shadow: 0 5px 10px #000;
  }
}
.chat-title-link {
  color: var(--ui-color-text-2);
  cursor: pointer;
  transition: color 0.2s linear, opacity 0.2s linear;
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin-bottom: 0.06rem;
  &:hover {
    color: var(--ui-color-brand1);
  }
  &:active {
    color: var(--ui-color-brand1);
    opacity: 0.6;
  }
  &.current {
    color: #d2c38c;
  }
}

.section-chat {
  pointer-events: all;
  // transition: all 0.4s ease-in-out;
  .chat-category-box {
    transform: translate3d(-100%, 0, 0);
    transition: transform 0.26s ease-in-out;
  }
  .chat-main {
    margin-left: -3.28rem;
    transition: margin-left 0.26s ease-in-out;
  }
  &.mini {
    .chat-dialog {
      width: 3.4rem;
    }
    .chat-category,
    .chat-cont-title {
      display: none;
    }
    .chat-main {
      margin-left: 0;
      transition: none;
    }
    .chat-textarea-ele {
      position: relative;
      textarea {
        height: 0.24rem;
        line-height: 0.24rem;
        width: calc(100% - 0.32rem);
      }
      .chat-textarea-actions {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 10;
      }
      .chat-button-send {
        width: 0.4rem;
        margin-right: 0;
      }
    }
    // .chat-tools-main {
    //   overflow: hidden;
    //   height: 0.3rem;
    // }
    .chat-button-new {
      min-width: 0.26rem;
    }
    .chat-button-new-text {
      display: none;
    }
    // .chat-label-item {
    //   margin-bottom: 0.2rem;
    // }

    .chat-action-tools {
      padding: 0.08rem 0.12rem 0.07rem 0.12rem;
    }
  }
  &:not(.mini) {
    animation: showChatBoxWrap 0.26s ease-in-out 0s 1 both;
    .chat-bg-mask {
      background: url('@src/assets/images/win/win-chat-bg-l.jpg') no-repeat 50%
        0;
      background-size: cover;
    }
    .window-title-desc {
      display: block;
    }
    .chat-dialog {
      animation: showChatBox 0.5s ease-in-out 0s 1 both;
    }
    .window-title-logo {
      animation: chatFadeIn 0.3s ease-in-out 0.3s 1 both;
    }
    .chat-category-inner {
      animation: chatFadeIn 0.3s ease-in-out 0.2s 1 both;
    }
    .message-menu-content {
      // transform: rotate(180deg);
      // animation: showMessageList2 0.6s cubic-bezier(0.4, 0.4, 0, 1.02) 0.4s 1
      //   both;
    }
    .chat-message-row {
      // animation: showMessageList2 0.6s cubic-bezier(0.4, 0.4, 0, 1.02) 0.5s 1
      //   both;
      // transform: rotate(180deg);
    }
    .chat-cont-inner {
      height: 62vh;
    }
  }
  &.showCategory {
    .chat-category-box {
      transform: translate3d(0, 0, 0);
    }
    .chat-category-button::before{
      transform: rotate(180deg);
    }
    .chat-cont-inner {
      height: 62vh;
    }
    .chat-main {
      margin-left: -1rem;
    }
  }
}

@keyframes showMessageList2 {
  0% {
    opacity: 0;
    transform: translate3d(0, 0.4rem, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}

@keyframes showChatBoxWrap {
  0% {
    top: 0.4rem;
    left: 0.8rem;
    transform: translate3d(0, 0, 0);
  }
  100% {
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
  }
}

@keyframes chatFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes showChatBox {
  0% {
    width: 3.4rem;
  }
  100% {
    width: 10.6rem;
  }
}
