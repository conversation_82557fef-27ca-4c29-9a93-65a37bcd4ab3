@use './markdown.scss';

.chat-messages {
  padding: 0.16rem 0.24rem 0.2rem 0.24rem;
  height: 100%;
  overflow: auto;
  margin: 0;
  font-weight: 300;
  margin-right: -0.09rem;
  -webkit-mask: linear-gradient(
    to bottom,
    transparent 0,
    #000 0.2rem,
    #000 calc(100% - 0.3rem),
    transparent 100%
  );

  &::-webkit-scrollbar-thumb {
    border: 0.016rem solid #2a2825;
    background: url('@src/assets/images/win/win-scrollbar.png') repeat 0 0;
  }
  &::-webkit-scrollbar-thumb:hover {
    border: 0 solid #2a2825;
    background: url('@src/assets/images/win/win-scrollbar-hover.png') repeat 0 0;
  }
}

.chat-message-load {
  text-align: center;
  padding-bottom: 0.12rem;
}
.chat-message-load-more {
  font-size: max(0.1rem, 10px);
  color: rgba($color: #cecdcb, $alpha: 0.6);
  text-align: center;
  cursor: pointer;
  transition: color 0.2s linear;
  position: relative;
  display: inline-block;
  span {
    // zoom: 0.85;
    opacity: 0.8;
    text-shadow: 0 0 5px black;
    position: relative;
    &::before {
      content: '';
      width: 0.16rem;
      height: 0.16rem;
      background: url('@src/assets/images/ui/icon-arrow-down.png');
      background-size: 100% 100%;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      right: -0.16rem;
      transition: opacity 0.2s linear;
      opacity: 1;
    }
    &::after {
      content: '';
      width: 0.16rem;
      height: 0.16rem;
      background: url('@src/assets/images/ui/icon-arrow-down-hover.png');
      background-size: 100% 100%;
      position: absolute;
      top: 50%;
      margin-top: -0.08rem;
      right: -0.16rem;
      transition: opacity 0.2s linear;
      opacity: 0;
    }
  }
  &:hover {
    color: rgba(255, 244, 180, 0.8);
    span::before {
      opacity: 0;
    }
    span::after {
      opacity: 1;
    }
  }
  &:active {
    color: rgba($color: #cecdcb, $alpha: 0.6);
  }
}

.chat-message-date {
  font-size: max(0.1rem, 10px);
  color: rgba($color: #cecdcb, $alpha: 0.6);
  padding-bottom: 0.06rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  span {
    // zoom: 0.85;
    opacity: 0.8;
    text-shadow: 0 0 5px black;
    position: relative;
    &::before,
    &::after {
      content: '';
      height: 1px;
      position: absolute;
      left: 0;
      width: 500%;
      left: calc(-500% - 0.2rem);
      top: 50%;
      background-color: rgba($color: #cecdcb, $alpha: 0.08);
    }
    &::after {
      left: auto;
      right: calc(-500% - 0.2rem);
    }
  }
}

.chat-message-row {
  margin: 0 0 0.16rem 0;
  &.message-reply.operations {
    padding: 0.12rem 0.16rem 0.12rem 0.16rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.05rem;
  }
}
.chat-message-item {
  font-size: 0.14rem;
  font-weight: 300;
  line-height: 1.6;
}
.message-mine {
  display: flex;
  justify-content: flex-end;
  .chat-message-item {
    padding: 0.07rem 0.1rem;
    display: inline-flex;
    background-color: #a89565;
    color: #000;
    border-radius: 0.06rem;
    border-top-right-radius: 0;
    font-weight: 400;
  }
}

.message-reply {
  .chat-message-item {
    // padding: 0.07rem 0rem 0 0;
    color: var(--ui-color-text-2);

    strong {
      color: var(--ui-color-text-1);
    }
  }
}

.message-menu-content {
  padding: 0.12rem 0.16rem 0.12rem 0.16rem;
  background-color: rgba($color: #ffffff, $alpha: 0.05);
  border-radius: 0.05rem;
  margin-top: 0.2rem;
}
.message-menu-head {
  display: flex;
  position: relative;
  padding: 0 0 0.12rem 0;
  margin-bottom: 0.12rem;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background-color: rgba($color: #ffffff, $alpha: 0.05);
  }
}
.message-menu-title {
  width: 100%;
  flex: 1;
  position: relative;
  overflow: hidden;
  font-size: 0.14rem;
  font-weight: bold;
  color: #d2c38c;
  display: flex;
  &::before {
    content: '';
    width: 0.24rem;
    height: 0.24rem;
    display: block;
    background: url('@src/assets/images/ui/icon-question.png');
    background-size: 100% 100%;
    margin: -0.02rem 0 0;
  }
}
.button-refresh {
  font-size: 0.12rem;
  color: rgba($color: #a8a4a0, $alpha: 0.8);
  transition: color 0.2s linear;
  cursor: pointer;
  padding: 0 0.12rem;
  &:last-child {
    padding-right: 0;
  }
  &:hover {
    color: #fff4b4;
  }
  &:active {
    color: rgba($color: #a8a4a0, $alpha: 0.8);
  }
}
.message-menu-link {
  font-size: 0.12rem;
  color: var(--ui-color-text-2);
  transition: color 0.2s linear;
  margin: 0 0 0.02rem 0;
  display: block;
  &:last-child {
    margin-bottom: 0;
  }
  &:hover {
    color: var(--ui-color-text-1);
  }
  &:active {
    color: var(--ui-color-text-2);
  }
}
.message-menu-link-text {
  cursor: pointer;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
}

.chat-message-list {
  // display: flex;
  // flex-direction: column-reverse;
  // .message-menu-content {
  //   animation: showMessageList 0.6s cubic-bezier(0.4, 0.4, 0, 1.02) 0s 1 both;
  // }
  // .chat-message-row {
  //   animation: showMessageList 0.6s cubic-bezier(0.4, 0.4, 0, 1.02) 0s 1 both;
  //   animation-delay: 0.2s;
  // }
}

.chat-loading {
  position: relative;
  width: 1rem;
  overflow: hidden;
  font-size: 0;
  padding: 0.12rem 0;
}
.chat-loading-icon {
  width: 0.06rem;
  height: 0.06rem;
  display: inline-block;
  background: #a89565;
  border-radius: 200px;
  margin: 0 0 0 0.06rem;
  animation: loadingDot 1s linear 0s infinite both;
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}
@keyframes loadingDot {
  0% {
    transform: scale(0.6);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.6);
    opacity: 0.2;
  }
}

@keyframes showMessageList {
  0% {
    opacity: 0;
    transform: translate3d(0, 0.4rem, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}

.chat-nodata {
  position: relative;
  padding: 0.12rem 0;
}
.chat-nodata-icon {
  width: 1.2rem;
  height: 1.2rem;
  display: block;
  background: url('@src/assets/images/ai-chat/nodata/nodata-icon-1.png') repeat
    0 0;
  background-size: contain;
  margin: 0 auto 0.12rem auto;
}
.network {
  .chat-nodata-icon {
    background: url('@src/assets/images/ai-chat/nodata/nodata-icon-4.png')
      repeat 0 0;
    background-size: contain;
  }
}
.chat-nodata-text {
  font-size: 0.14rem;
  color: rgba($color: #c5c1bd, $alpha: 0.8);
  text-align: center;
}

.chat-thinking {
  margin-bottom: 0.2rem;
  font-size: 0.12rem;
  &.expand {
    .chat-thinking-tit {
      margin-bottom: 0.1rem;
    }
    .chat-thinking-tit::after {
      transform: rotate(0);
    }
    .chat-thinking-content {
      max-height: 9999px;
    }
  }
  &.collapse {
    .chat-thinking-tit::after {
      transform: rotate(180deg);
    }
    .chat-thinking-content {
      max-height: 0;
    }
  }
}

.chat-thinking-tit {
  display: inline-flex;
  align-items: center;
  color: #c5c1bd;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s linear;
  &:hover {
    opacity: 1;
  }
  &::after {
    content: '';
    display: block;
    margin-left: 0.06rem;
    width: 0.12rem;
    height: 0.12rem;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAQFJREFUWEftlVESgiAQhkVvVVNJTeeqztWUWFPHklwnGCQEdqXxBd4cle/j3wVYsfBgC/OLLJATyAnoBJ7Nta6q6iSlFOvd4fKP7fl+3BuYv+u6dsOPAhha4NXepIIyxsRqu+cpJb7weoAa82sB8wP7o7ki9tz9fGeV8qgEZVk2JixFEg540ZeA/5QAwNAHKSVC8FEPqJWnkoiBOwVSJBELnxSYI4GBewUoElh4UAAjQYFHCcRIUOHRAj4JeNcfr8MJp4a5z0OHGOo2dG1RG4CBoxLwnROUlat/UAn4JLArnyVg9wQVTiqBWXPoCXhWF0uo4VzvSSWggKb+yQI5gZzAByR04SF9M7eQAAAAAElFTkSuQmCC')
      no-repeat;
    background-size: contain;
    // transition: transform 0.2s ease-in-out;
  }
}

.chat-thinking-content {
  position: relative;
  padding-left: 0.2rem;
  overflow: hidden;
  // transition: max-height 0.3s ease-in-out;
  max-height: 9999px;
  &::before {
    content: '';
    position: absolute;
    left: 2px;
    height: 100%;
    width: 2px;
    background: rgba(255, 255, 255, 0.1);
  }
  &.analysis-markdown {
    opacity: 0.6;
    font-size: 0.12rem;
  }
}

.section-chat {
  .chat-message-list {
    transform: rotate3d(0, 0, 1, 180deg) translateZ(0);
    transform-origin: center;
    transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform;
  }
  
  .message-menu-content,
  .chat-message-row,
  .chat-message-date {
    transform: rotate3d(0, 0, 1, 180deg) translateZ(0);
    transform-origin: center;
    transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform;
    div, p, h1, h2, h3, h4, h5, h6{
      transform: translateZ(0);
      transform-origin: center;
      transform-style: preserve-3d;
      -webkit-backface-visibility: hidden;
      perspective: 1000px;
      will-change: transform;
    }
  }
  &:not(.mini) {
    .chat-message-list {
      transform: rotate3d(0, 0, 1, 180deg) translateZ(0);
      transform-origin: center;
      transform-style: preserve-3d;
      -webkit-backface-visibility: hidden;
      perspective: 1000px;
      will-change: transform;
    }
    
    .message-menu-content,
    .chat-message-row,
    .chat-message-date {
      transform: rotate3d(0, 0, 1, 180deg) translateZ(0);
      transform-origin: center;
      transform-style: preserve-3d;
      -webkit-backface-visibility: hidden;
      perspective: 1000px;
      will-change: transform;
      div, p, h1, h2, h3, h4, h5, h6{
        transform: translateZ(0);
        transform-origin: center;
        -webkit-backface-visibility: hidden;
        -webkit-font-smoothing: subpixel-antialiased;
      }
    }
  }
}


