@charset "UTF-8";

.analysis-markdown {
  --mk-color-brand-1: #a89565;
  --mk-color-link-hover: #a89565;
  --mk-color-fill-1: #39393b;
  --mk-color-fill-2: #39393b;
  --mk-color-text-1: #C5C1BD;
  --mk-color-text-2: rgba(197, 193, 189, 0.8);
  --mk-color-blockquote: rgb(206, 205, 203, 0.8);
  --mk-color-blockquote-border: rgb(206, 205, 203, 0.1);
  --mk-color-fill-blockquote: rgb(206, 205, 203, 0.05);

  line-height: 1.7;
  color: var(--mk-color-text-2);
  word-break: break-all;
  font-size: 0.14rem;
  font-weight: 300;
}
.analysis-markdown h1,
.analysis-markdown h2,
.analysis-markdown h3,
.analysis-markdown h4,
.analysis-markdown h5,
.analysis-markdown h6,
.analysis-markdown .h1,
.analysis-markdown .h2,
.analysis-markdown .h3,
.analysis-markdown .h4,
.analysis-markdown .h5,
.analysis-markdown .h6 {
  font-family: inherit;
  font-weight: 700;
  line-height: 1.1;
  color: inherit;
  color: var(--mk-color-text-1);
}
.analysis-markdown h1,
.analysis-markdown h2,
.analysis-markdown h3 {
  margin-top: 0.3rem;
  margin-bottom: 0.16rem;
}
.analysis-markdown h4,
.analysis-markdown h5,
.analysis-markdown h6 {
  margin-top: 0.12rem;
  margin-bottom: 0.12rem;
}
.analysis-markdown h1,
.analysis-markdown .h1 {
  font-size: 2em;
}
.analysis-markdown h2,
.analysis-markdown .h2 {
  font-size: 1.5em;
}
.analysis-markdown h3,
.analysis-markdown .h3 {
  font-size: 1.25em;
}
.analysis-markdown h4,
.analysis-markdown .h4 {
  font-size: 1em;
}
.analysis-markdown h5,
.analysis-markdown .h5 {
  font-size: 0.875em;
}
.analysis-markdown h6,
.analysis-markdown .h6 {
  font-size: 0.85em;
}
.analysis-markdown b,
.analysis-markdown strong {
  // font-weight: bold;
}
.analysis-markdown ul,
.analysis-markdown ol {
  padding-left: 0.24rem;
  margin-bottom: 0.16rem;
  line-height: 2;
}
.analysis-markdown ul {
  list-style-type: disc;
  ul {
    list-style-type: circle;
  }
}
.analysis-markdown ol {
  list-style-type: decimal;
}
.analysis-markdown ul ul,
.analysis-markdown ul ol,
.analysis-markdown ol ul,
.analysis-markdown ol ol {
  margin-bottom: 0;
}
.analysis-markdown ul li,
.analysis-markdown ol li {
  list-style: inherit;
}
.analysis-markdown ul li p,
.analysis-markdown ol li p {
  margin: 0;
}
.analysis-markdown div ul,
.analysis-markdown div ol {
  margin-bottom: 0;
}
.analysis-markdown hr {
  height: 0;
  border: 0;
  border-top: 1px solid rgba($color: #cecdcb, $alpha: 0.08);
  margin: 0.16rem 0;
  box-sizing: content-box;
  overflow: visible;
}

.analysis-markdown table {
  border-collapse: collapse;
}
.analysis-markdown table th,
.analysis-markdown table td {
  border: 1px solid var(--mk-color-fill-1);
  padding: 0.04rem 0.1rem;
  min-width: 1rem;
}
.analysis-markdown table th {
  background-color: var(--mk-color-fill-2);
}
.analysis-markdown .link-quote {
  color: var(--mk-color-brand-1);
}
.analysis-markdown a {
  color: var(--mk-color-brand-1);
  position: relative;
  text-decoration: none;
}
.analysis-markdown a[target="_blank"] {
  padding: 0 0.02rem;
}
// .analysis-markdown a[target=_blank]::after {
//   content: "\ea10";
//   font-size: 0.12rem;
//   font-family: "ch-icon";
//   margin: 0 2px;
// }
.analysis-markdown a:hover {
  color: var(--mk-color-link-hover);
}
.analysis-markdown em {
  font-style: italic;
}
.analysis-markdown sup {
  vertical-align: super;
}
.analysis-markdown sub {
  vertical-align: sub;
}
.analysis-markdown figure {
  overflow-x: auto;
}
.analysis-markdown p,
.analysis-markdown pre,
.analysis-markdown blockquote,
.analysis-markdown table {
  margin: 0 0 0.16rem;
}
.analysis-markdown blockquote {
  color: var(--mk-color-blockquote);
  padding: 0.1rem 0.15rem;
  border-left: 3px solid var(--mk-color-blockquote-border);
  background: var(--mk-color-fill-blockquote);
  font-size: 0.12rem;
}
.analysis-markdown blockquote p,
.analysis-markdown blockquote blockquote,
.analysis-markdown blockquote table,
.analysis-markdown blockquote pre,
.analysis-markdown blockquote ul,
.analysis-markdown blockquote ol {
  margin: 0;
}
.analysis-markdown pre {
  padding: 0.16rem;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: rgba($color: #fff, $alpha: 0.05);
  border-radius: 0.06rem;
  font-size: 0.14rem;
}

.analysis-markdown .pln {
  color: var(--mk-color-fill-1);
}
.analysis-markdown .str {
  color: #ffaf21;
}
.analysis-markdown .kwd {
  color: #f85353;
}

.analysis-markdown {
  h1:nth-child(1),
  h2:nth-child(1),
  h3:nth-child(1),
  h4:nth-child(1) {
    margin-top: 0;
    padding-top: 0;
  }
}
