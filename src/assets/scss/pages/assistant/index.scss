@use './role-select.scss';

html {
  font-size: calc(100vw / 1920) * 100;
  height: 100%;
}
.page-frame {
  position: fixed;
  inset: 0;
  left: 0rem;
  top: 1rem;
  z-index: 100;
  overflow: hidden;
}
.page-frame.page-frame--guide {
  pointer-events: none;
  z-index: 200;
  top: 0.8rem;
  left: 0;
  overflow: visible;
}
.section-ddj {
  width: 0.8rem;
  height: 0.8rem;
  position: absolute;
  top: 0.2rem;
  left: 0rem;
  z-index: 20;
  transition: left 0.3s ease-in-out;
  pointer-events: all;
}
.section-ddj-avatar {
  position: absolute;
  inset: 0;
  z-index: 4;
  background: url('@src/assets/images/ai-chat/avatar-ddj.png');
  background-size: 100% 100%;
  cursor: pointer;
}

.section-ddj-lit {
  width: 116%;
  position: absolute;
  top: -0.3rem;
  left: -0.06rem;
  aspect-ratio: 700 / 900;
  z-index: 5;
  background: url('@src/assets/images/ai-chat/avatar-ddj-lit.png');
  background-size: 100% 100%;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.section-ddj-light {
  width: 143%;
  position: absolute;
  top: -0.3rem;
  left: -0.15rem;
  aspect-ratio: 860 / 940;
  z-index: 2;
  background: url('@src/assets/images/ai-chat/avatar-ddj-light.png');
  background-size: 100% 100%;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.section-ddj-name {
  width: 106%;
  position: absolute;
  top: 0.2rem;
  left: 0;
  aspect-ratio: 86 / 183;
  display: block;
  background: url('@src/assets/images/ai-chat/avatar-ddj-flag-bg.png');
  background-size: 100% 100%;
  opacity: 0;
  transition: opacity 0s ease-in-out;
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: url('@src/assets/images/ai-chat/avatar-ddj-flag-name.png');
    background-size: 100% 100%;
  }
}

.section-guide {
  width: 4.36rem;
  position: absolute;
  top: 0.2rem;
  z-index: 10;
  font-size: 0.14rem;
  pointer-events: all;
}

.section-guide-cont {
  min-height: 0.36rem;
  padding: 0 1rem 0 0.8rem;
  margin: 0.24rem 0 0.2rem;
  position: relative;
  z-index: 2;
}
.guide-tips-cont {
  padding: 0 0.26rem 0 0;
  margin: 0 -0.3rem 0 0;
  max-height: 4rem;
  overflow: scroll;
  overflow-x: hidden;
  color: var(--ui-color-text-2);
  p {
    padding-bottom: 0.12rem !important;
    text-align: left;
    &:last-child {
      padding-bottom: 0 !important;
    }
    strong{
      margin: 0 0.04rem;
      color: #d2c38c;
    }
  }
}
.section-tips-words {
  line-height: 1.6;
  text-align: justify;
  padding: 0 0 0.1rem 0;
  p {
    padding: 0 0 0.1rem 0;
    &:last-child {
      padding-bottom: 0;
    }
  }
  &:last-child {
    padding-bottom: 0;
    p {
      padding: 0;
    }
  }
}

.button-guide-close {
  width: 0.4rem;
  height: 0.4rem;
  cursor: pointer;
  display: block;
  position: absolute;
  right: 0.55rem;
  top: 0.05rem;
  z-index: 10;
  &::before,
  &::after {
    content: '';
    width: 0.3rem;
    height: 0.3rem;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -0.15rem 0 0 -0.15rem;
    transition: opacity 0.3s linear;
  }
  &::before {
    background: url('@src/assets/images/ai-chat/icon-close-guide.png');
    background-size: 100% 100%;
  }
  &::after {
    background: url('@src/assets/images/ai-chat/icon-close-guide-hover.png');
    background-size: 100% 100%;
    opacity: 0;
    z-index: 2;
    transition: opacity 0.3s linear, transform 0.3s linear;
  }
  &:hover {
    &::before {
      opacity: 0;
    }
    &::after {
      opacity: 1;
    }
  }
  &:active {
    &::before {
      opacity: 0;
    }
    &::after {
      opacity: 1;
      transform: scale(0.7);
    }
  }
}

.section-guide-bg {
  width: 4.28rem;
  position: absolute;
  inset: 0;
  z-index: 1;
  pointer-events: none;
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 0.32rem;
  }
  &::before {
    top: 0;
    background: url('@src/assets/images/ai-chat/float-border-t1.png');
    background-size: 100% 100%;
  }
  &::after {
    bottom: 0;
    background: url('@src/assets/images/ai-chat/float-border-b1.png');
    background-size: 100% 100%;
  }
  span {
    position: absolute;
    inset: 0;
    top: 0.32rem;
    bottom: 0.32rem;
    background: url('@src/assets/images/ai-chat/float-border-m1.png');
    background-size: 100% 100%;
  }
}

.guide-tips-title {
  margin-bottom: 0.12rem;
  font-size: 0.18rem;
  font-weight: bold;
  color: #d2c38c;
  position: relative;
}

.guide-tips-tail {
  color: #d2c38c;
  cursor: pointer;
  margin-bottom: 0.12rem;
  transition: color 0.2s linear;
  &:hover {
    color: #e1d3a0;
  }
}

.section-ddj {
  &.stage01 {
    left: -0.26rem;
    transform: rotate(12deg);
    animation: showDDJ 0.6s cubic-bezier(0.59, 0.34, 0.03, 1.43) 0s 1 normal
      both;
    &:hover {
      .section-ddj-light {
        opacity: 1;
      }
    }
  }

  &.stage02 {
    left: -0.26rem;
    transform: rotate(12deg);
    &:hover {
      .section-ddj-light {
        opacity: 1;
      }
    }
  }
  &.stage03 {
    left: 0;
    transform: rotate(0deg);
    .section-ddj-avatar {
      background: url('@src/assets/images/ai-chat/avatar-ddj-1.png');
      background-size: 100% 100%;
      animation: avatarAnimation 1s linear 0s infinite normal both;
    }
    .section-ddj-light {
      opacity: 1;
    }
    .section-ddj-lit {
      opacity: 1;
      animation: litAnimation 1s linear 0s infinite normal both;
    }
  }
  &.stage05,
  &.stage04 {
    transition: none;
    left: 0;
    transform: rotate(0deg);
    .section-ddj-avatar {
      background: url('@src/assets/images/ai-chat/avatar-ddj-1.png');
      background-size: 100% 100%;
    }
    .section-ddj-name {
      transition: opacity 0.3s ease-in-out;
      opacity: 1;
    }
    .section-ddj-light {
      opacity: 1;
    }
    .section-ddj-lit {
      opacity: 1;
    }
  }
  &.stage05 {
    opacity: 0;
  }
}

@keyframes avatarAnimation {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, 0.05rem, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes litAnimation {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -0.05rem, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes showDDJ {
  0% {
    // transform: rotate(12deg);
    transform: rotate(12deg) translate3d(-100%, 0, 0);
  }
  100% {
    transform: rotate(12deg) translate3d(0, 0, 0);
  }
}

.section-guide.show {
  .button-guide-close {
    opacity: 0;
    animation: fadeIn 0.3s linear 0.6s 1 normal both;
  }
  .section-guide-cont {
    opacity: 0;
    animation: fadeIn 0.3s linear 0.6s 1 normal both;
  }
  .section-guide-bg {
    bottom: auto;
    height: 0.64rem;
    animation: showGuideTipsBg 0.3s ease-in-out 0.3s 1 normal both;
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes showGuideTipsBg {
  0% {
    opacity: 0;
    height: 0.64rem;
  }
  100% {
    opacity: 1;
    height: 100%;
  }
}
