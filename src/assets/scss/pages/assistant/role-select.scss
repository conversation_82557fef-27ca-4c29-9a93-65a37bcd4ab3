.role-select-avatar {
  min-width: 0.72rem;
  width: 0.72rem;
  height: 0.72rem;
  padding: 0.03rem;
  background: url("@src/assets/images/ui/avatar-border-primary.png");
  background-size: 100% 100%;
  img {
    width: 100%;
    height: 100%;
    display: block;
  }
}
.role-select-cont {
  width: 100%;
  flex: 1;
  overflow: hidden;
  padding: 0 0 0 0.14rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.role-select-title {
  font-size: 0.18rem;
  font-weight: bold;
  color: var(--ui-color-text-2);
  text-shadow: 0 0 5px black;
}
.role-select-describition {
  color: #6d6dcc;
  font-size: 0.14rem;
  text-shadow: 0 5px 10px #000;
  font-weight: 400;
}
.role-select-item {
  display: flex;
  margin: 0 0 0.12rem 0;
}
