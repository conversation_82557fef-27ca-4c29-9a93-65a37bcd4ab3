@use '@src/assets/scss/components/bd-panel.scss';
@use '@src/assets/scss/components/analysis-panel.scss';

html {
  font-size: calc(100vw / 1920) * 100;
  height: 100%;
}


.page-frame {
  position: fixed;
  inset: 0;
  left: 0;
  top: 0;
  z-index: 100;
  overflow: hidden;
}

.page-frame--equipment {
  // slot-color-1 颜色一:传奇
  // slot-color-2 颜色二:稀有
  // slot-color-3 颜色三:魔法
  --slot-color-1: #f06916;
  --slot-color-2: #fee055;
  --slot-color-3: #7a97fe;

  --line-color-1: #863E1F;
  --line-color-2: #6F542B;
  --line-color-3: #5B4498;

  --tips-color-1: #cd793c;
  --tips-color-2: #d2c38c;
  --tips-color-3: rgb(197, 193, 189, 0.8);
  --tips-color-4: #8787fe;
  --tips-color-5: #fff;

  pointer-events: none;

  bottom: 20px;

  .page-frame-cont {
    width: 4.48rem;
    position: absolute;
    right: 6.6rem;
    top: 0;
    bottom: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    pointer-events: all;
  }

  .bd-panel {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow: hidden;
    background: url('@src/assets/images/win/eqbd-panel-main.png') no-repeat;
    background-size: 100% calc(100% - 3.51rem - 1.45rem);
    background-position-y: 3.51rem;

    &::before {
      height: 3.52rem;
      background: url('@src/assets/images/win/eqbd-panel-before.png');
      background-size: 100% 100%;
    }

    &::after {
      height: 1.46rem;
      background: url('@src/assets/images/win/eqbd-panel-after.png');
      background-size: 100% 100%;
    }
  }

  .analysis-panel {
    width: 100%;
    height: auto;
    min-height: 3.55rem;
    min-height: 3.05rem;
    margin-top: -0.2rem;
    background: url('@src/assets/images/win/eq-analysis-panel-main.png') no-repeat;
    background-size: 100% calc(100% - 1.47rem - 1.04rem);
    background-position-y: 1.47rem;

    &::before {
      background: url('@src/assets/images/win/eq-analysis-panel-before.png');
      background-size: 100% 100%;
    }

    &::after {
      background: url('@src/assets/images/win/eq-analysis-panel-after.png');
      background-size: 100% 100%;
    }
  }

  .analysis-hd {
    padding: 0 0.2rem 0 0.32rem;
  }

  .analysis-bd {
    margin-right: 0;
  }

  // .analysis-markdown.bd-analysis-markdown {
  //   margin: 0.08rem 0;
  //   padding: 0 0.2rem 0 0.42rem;
  //   color: rgba(197, 193, 189, 0.8);
  //   text-align: justify;
  //   h2 {
  //     font-size: 0.18rem;
  //     font-weight: bold;
  //     padding: 0 0 0.12rem;
  //     margin: 0;
  //   }
  //   h3 {
  //     font-size: 0.14rem;
  //     font-weight: bold;
  //     padding: 0 0 0.08rem;
  //     margin: 0;
  //   }
  //   p {
  //     padding-bottom: 0.16rem;
  //   }
  // }
}

// slot-level-1 颜色一:传奇
// slot-level-2 颜色二:稀有
// slot-level-3 颜色三:魔法

.slot-level-1 {
  --slot-border-color: var(--slot-color-1);
}

.slot-level-2 {
  --slot-border-color: var(--slot-color-2);
}

.slot-level-3 {
  --slot-border-color: var(--slot-color-3);
}

.equipment-panel-wrap {
  height: 100%;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.eq-panel-hd {
  width: 100%;
  height: 0.48rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.eq-panel-bd {
  height: calc(100% - 0.48rem);
  padding: 0.1rem 0.36rem 0.22rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.equipment-box-wrap {
  flex: 1;
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  margin: 0 -0.24rem 0 -0.3rem;
  padding: 0 0.24rem 0 0.3rem;
}

.eq-panel-tit {
  width: 2.17rem;
  height: 0.31rem;
  background: url('@src/assets/images/equipment/panel-title.png') no-repeat;
  background-size: contain;
  margin-top: 0.1rem;
}

.equipment-box {
  width: 100%;
  aspect-ratio: 1 / 1;
  position: relative;
  --slot-width-1: 80;
  --slot-width-2: 40;
  --slot-width-3: 140;
}

.equipment-box-slot {
  width: 4.2rem;
  aspect-ratio: 1 / 1;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -2rem;
  display: block;
  background: url('@src/assets/images/equipment/box-bg.png') no-repeat;
  background-size: contain;
}

.equipment-slot {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  cursor: default;

  .equipment-source::before {
    opacity: 0.3;
  }

  &:hover {
    &>.equipment-source::before {
      opacity: 0.6;
    }
  }
}

.slot-body-armour {
  width: calc((var(--slot-width-1) / 420) * 100%);
  aspect-ratio: var(--slot-width-1) / 122;
  margin: -0.28rem 0 0 -0.08rem;
}

.slot-helmet {
  width: calc((var(--slot-width-1) / 420) * 100%);
  aspect-ratio: var(--slot-width-1) / var(--slot-width-1);
  margin: -1.38rem 0 0 -0.08rem;
}

.slot-belt {
  width: calc((var(--slot-width-1) / 420) * 100%);
  aspect-ratio: var(--slot-width-1) / 38;
  margin: 0.65rem 0 0 -0.08rem;
}

.slot-gloves {
  width: calc((var(--slot-width-1) / 420) * 100%);
  aspect-ratio: var(--slot-width-1) / var(--slot-width-1);
  margin: 0.44rem 0 0 -1rem;
}

.slot-boots {
  width: calc((var(--slot-width-1) / 420) * 100%);
  aspect-ratio: var(--slot-width-1) / var(--slot-width-1);
  margin: 0.44rem 0 0 0.84rem;
}

.slot-ring-left {
  width: calc((var(--slot-width-2) / 420) * 100%);
  aspect-ratio: var(--slot-width-2) / var(--slot-width-2);
  margin: -0.28rem 0 0 -0.8rem;
}

.slot-ring-right {
  width: calc((var(--slot-width-2) / 420) * 100%);
  aspect-ratio: var(--slot-width-2) / var(--slot-width-2);
  margin: -0.28rem 0 0 0.64rem;
}

.slot-amulet {
  width: calc((var(--slot-width-2) / 420) * 100%);
  aspect-ratio: var(--slot-width-2) / var(--slot-width-2);
  margin: -0.78rem 0 0 0.64rem;
}

.slot-weapons-left {
  width: calc((var(--slot-width-1) / 420) * 100%);
  aspect-ratio: var(--slot-width-1) / 164;
  margin: -0.88rem 0 0 -1.5rem;
}

.slot-weapons-right {
  width: calc((var(--slot-width-1) / 420) * 100%);
  aspect-ratio: var(--slot-width-1) / 164;
  margin: -0.88rem 0 0 1.35rem;
}

.slot-mana-flask {
  width: calc((var(--slot-width-2) / 420) * 100%);
  aspect-ratio: var(--slot-width-2) / 82;
  margin: 1.37rem 0 0 0.9rem;
}

.slot-life-flask {
  width: calc((var(--slot-width-2) / 420) * 100%);
  aspect-ratio: var(--slot-width-2) / 82;
  margin: 1.37rem 0 0 -1.05rem;
}

.slot-charms {
  width: calc((var(--slot-width-3) / 420) * 100%);
  aspect-ratio: var(--slot-width-3) / 40;
  margin: 1.36rem 0 0 -0.06rem;
  display: flex;
}

.slot-charm-1,
.slot-charm-2,
.slot-charm-3 {
  transform: none;
  width: calc((var(--slot-width-2) / var(--slot-width-3)) * 100%);
  aspect-ratio: var(--slot-width-2) / var(--slot-width-2);
}

.slot-charm-1 {
  top: 0;
  left: 0;
}

.slot-charm-2 {
  top: 0;
  transform: translate3d(-50%, 0, 0);
}

.slot-charm-3 {
  top: 0;
  left: auto;
  right: 0;
}

.equipment-tab-main,
.equipment-tab-secondary {
  width: 0.24rem;
  height: 0.2rem;
  position: absolute;
  z-index: 3;
  cursor: pointer;
  top: -0.15rem;
  left: 50%;
  transition: filter 0.2s linear;

  &.current,
  &:hover {
    filter: brightness(1.4);
  }

  &.current {
    filter: brightness(1.8);
  }
}

.equipment-tab-main {
  margin-left: -0.24rem;
  background: url('@src/assets/images/equipment/icon-eq-i-normal.png') no-repeat 50% 0;
  background-size: contain;
}

.equipment-tab-secondary {
  margin-left: 0rem;
  background: url('@src/assets/images/equipment/icon-eq-ii-normal.png') no-repeat 50% 0;
  background-size: contain;
}

.equipment-source {
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, #171615 0%, #1e1d19 100%);
  z-index: 5;
  padding: 0.03rem;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border: 0.01rem solid var(--slot-border-color, rgba(255, 255, 255, 0.8));
    opacity: 0.2;
    transition: opacity 0.2s linear;
  }

  img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: contain;
    object-position: 50% 50%;
  }
}

.equipment-box-tips{
  margin-top: 0.36rem;
}

.equipment-tips-title {
  color: rgba($color: #c5c1bd, $alpha: 0.6);
  font-size: 0.14rem;
  padding: 0.3rem 0 0.08rem 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0.11rem;
    aspect-ratio: 388 / 7;
    background: url('@src/assets/images/equipment/box-split.png') no-repeat 50% 0;
    background-size: 100% auto;
  }
}

.equipment-card {
  position: absolute;
  top: 2rem;
  left: 0.5rem;
  min-width: 3.3rem;
  z-index: 1000;
  border: 1px solid #3e3528;
  background: #040303;
  padding: 0.03rem;
  box-shadow: 0 0 10px 2px rgba($color: #000000, $alpha: 0.8);
  pointer-events: none;
  &.card-level-0 {
    min-width: 2.48rem;
  }
}

.equipment-card-inner {
  min-height: 1.5rem;
  background: url('@src/assets/images/equipment/popover/popover-bgimg.png') no-repeat 50% 0;
  background-size: 100% 100%;
  padding: 0.05rem;
}

.equipment-card-title {
  height: 0.44rem;
  position: relative;
}

.equipment-card-title-text {
  position: relative;
  z-index: 5;
}

.equipment-card-title-bg {
  position: absolute;
  inset: 0;
  z-index: 1;
  background: url('@src/assets/images/equipment/popover/color1-middle.png') repeat-x 50% 0;
  background-size: auto 0.44rem;

  &::before,
  &::after {
    content: '';
    width: 0.35rem;
    height: 100%;
    position: absolute;
    top: 0;
    z-index: 2;
  }

  &::before {
    left: 0;
    background: url('@src/assets/images/equipment/popover/color1-left.png') no-repeat 50% 0;
    background-size: auto 0.44rem;
  }

  &::after {
    right: 0;
    background: url('@src/assets/images/equipment/popover/color1-right.png') no-repeat 50% 0;
    background-size: auto 0.44rem;
  }
}

.equipment-card-title-text {
  color: var(--slot-color-1);
  font-size: 0.16rem;
  font-weight: 400;
  height: 0.44rem;
  padding-bottom: 0.04rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.equipment-card-tips-group {
  font-size: 0.14rem;
  text-align: center;
  padding: 0.05rem 0;
  position: relative;

  .tips-level-1 {
    color: var(--tips-color-1);
  }

  .tips-level-2 {
    color: var(--tips-color-2);
  }

  .tips-level-3 {
    color: var(--tips-color-3);
  }

  .tips-level-4 {
    color: var(--tips-color-4);
  }

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 0.01rem;
    background-color: var(--slot-color-1);
    -webkit-mask: linear-gradient(to right,
        transparent 0%,
        #000 30%,
        #000 calc(100% - 30%),
        transparent 100%);
  }

  &:last-child {
    &::after {
      display: none;
    }
  }
}

.card-level-0 {
  .equipment-card-tips-group {
    &::after {
      background-color: #5F5A55;
    }
  }

  .equipment-card-title-text {
    color: #C8C8C8;
  }

  .equipment-card-title-bg {
    background: url('@src/assets/images/equipment/popover/color0-middle.png') repeat-x 50% 0;
    background-size: auto 0.44rem;

    &::before {
      left: 0;
      background: url('@src/assets/images/equipment/popover/color0-left.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }

    &::after {
      right: 0;
      background: url('@src/assets/images/equipment/popover/color0-right.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }
  }
}

.card-level-1 {
  .equipment-card-tips-group {
    &::after {
      background-color: var(--line-color-1);
    }
  }

  .equipment-card-title-text {
    color: var(--slot-color-1);
  }

  .equipment-card-title-bg {
    background: url('@src/assets/images/equipment/popover/color1-middle.png') repeat-x 50% 0;
    background-size: auto 0.44rem;

    &::before {
      left: 0;
      background: url('@src/assets/images/equipment/popover/color1-left.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }

    &::after {
      right: 0;
      background: url('@src/assets/images/equipment/popover/color1-right.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }
  }
}

.card-level-2 {
  .equipment-card-tips-group {
    &::after {
      background-color: var(--line-color-2);
    }
  }

  .equipment-card-title-text {
    color: var(--slot-color-2);
  }

  .equipment-card-title-bg {
    background: url('@src/assets/images/equipment/popover/color2-middle.png') repeat-x 50% 0;
    background-size: auto 0.44rem;

    &::before {
      left: 0;
      background: url('@src/assets/images/equipment/popover/color2-left.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }

    &::after {
      right: 0;
      background: url('@src/assets/images/equipment/popover/color2-right.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }
  }
}

.card-level-3 {
  .equipment-card-tips-group {
    &::after {
      background-color: var(--line-color-3);
    }
  }

  .equipment-card-title-text {
    color: var(--slot-color-3);
  }

  .equipment-card-title-bg {
    background: url('@src/assets/images/equipment/popover/color3-middle.png') repeat-x 50% 0;
    background-size: auto 0.44rem;

    &::before {
      left: 0;
      background: url('@src/assets/images/equipment/popover/color3-left.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }

    &::after {
      right: 0;
      background: url('@src/assets/images/equipment/popover/color3-right.png') no-repeat 50% 0;
      background-size: auto 0.44rem;
    }
  }
}

.eq-tip-title {
  min-width: 0.5rem;
  position: relative;
  display: inline-flex;
  align-items: center;
  vertical-align: top;
  padding-right: 0.2rem;
  font-size: 0.14rem;
  padding: 0 0 0.04rem 0;

  &::after {
    content: '';
    width: 0.14rem;
    height: 0.14rem;
    margin: 0 0.06rem;
    background: url('@src/assets/images/equipment/icon-tips-dot.png') no-repeat 50% 0;
    background-size: contain;
  }
}

.eq-tip-desc {
  display: inline-block;
  vertical-align: top;
  font-size: 0.14rem;
  width: 80%;
}

.equipment-tips-group {
  padding: 0 0 0.05rem 0;
}

.tips-level-1 {

  .eq-tip-desc,
  .eq-tip-title {
    color: var(--tips-color-1);
  }
}

.tips-level-2 {

  .eq-tip-desc,
  .eq-tip-title {
    color: var(--tips-color-2);
  }
}

.tips-level-3 {

  .eq-tip-desc,
  .eq-tip-title {
    color: var(--tips-color-3);
  }
}

.equipment-analysis-panel {
  .bd-panel-close {
    display: none;
  }
}

.hidePanel {
  .equipment-analysis-panel {
    .bd-panel-close {
      display: block;
      top: 0.04rem;
      right: -0.08rem;
    }
  }
}

.win-client-entry {
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 3.54rem;
  aspect-ratio: 354 / 53;
  cursor: pointer;
  pointer-events: all;

  &::before,
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    transition: opacity 0.2s linear;
  }
  

  &::before {
    opacity: 1;
  }

  &::after {
    opacity: 0;
  }

  &:hover {
    &::after {
      opacity: 1;
    }
  }
  &.equipment{
    right: 7rem;
    &::before {
      background: url('@src/assets/images/win/win-entry-equip.png') no-repeat 50% 0;
      background-size: contain;
    }
  
    &::after {
      background: url('@src/assets/images/win/win-entry-equip-hover.png') no-repeat 50% 0;
      background-size: contain;
    }
  }
}

.equipment-analysis-panel {
  .analysis-title {
    width: 1.04rem;
    background: url("@src/assets/images/ui/analysis-title--equip.png") no-repeat;
    background-size: contain;
  }
}