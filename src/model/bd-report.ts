import { WithCommonResult } from '@src/types/common';
import { baseRequest } from '@tencent/wegame-web-sdk';
import { FROM_SRC } from './common';

const BASE_URL = '/api/v1/wegame.pallas.poe2.Poe2BaseInfo';

// 推荐BD上报记录请求参数
export interface RecommendBDReportReq {
  bd_id: string;
  character: string;
  game_version: string;
}

// 推荐BD上报记录响应
export type RecommendBDReportRsp = Record<string, never>;

// 获取推荐上报BD请求参数
export interface GetRecommendBDReq {
  from_src: string;
  character: string;
  game_version: string;
}

// 获取推荐上报BD响应
export interface GetRecommendBDRsp {
  bd_id: string;
}

/**
 * 推荐BD上报记录
 * @param params 上报参数
 * @returns 上报结果
 */
export async function recommendBDReport(
  bd_id: string,
  character: string,
  game_version: string
): Promise<RecommendBDReportRsp | null> {
  try {
    const res = await baseRequest<WithCommonResult<RecommendBDReportRsp>>({
      url: `${BASE_URL}/RecommendBDReport`,
      method: 'POST',
      data: {
        from_src: FROM_SRC,
        bd_id,
        character,
        game_version
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return res;
  } catch (error) {
    console.error('recommend BD report failed:', error);
  }
  return null;
}

/**
 * 获取推荐上报BD
 * @param params 请求参数
 * @returns BD ID
 */
export async function getRecommendBD(
  character: string,
  game_version: string
): Promise<GetRecommendBDRsp | null> {
  try {
    const res = await baseRequest<WithCommonResult<GetRecommendBDRsp>>({
      url: `${BASE_URL}/GetRecommendBD`,
      method: 'POST',
      data: {
        from_src: FROM_SRC,
        character,
        game_version
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return res;
  } catch (error) {
    console.error('get recommend BD failed:', error);
  }
  return null;
}
