import { WithCommonResult } from '@src/types/common';
import { baseRequest } from '@tencent/wegame-web-sdk';
import { FROM_SRC } from './common';

const BASE_URL = '/api/v1/wegame.pallas.poe2.Poe2BaseInfo';

// 翻译映射接口
export interface TranslateMap {
  [key: string]: string;
}

// BD 列表信息接口
export interface BdListInfo {
  bd_id: string;
  class_id: string;
  ascendancy_id: string;
  title: string;
  author: string;
  tags: string[];
}
/**
 * 根据职业ID查询BD列表
 * @param classId 职业ID
 * @param ascendancyId 升华职业ID
 * @returns BD列表
 */
export async function getBdList(
  classId?: string,
  ascendancyId?: string
): Promise<{ bd_list: BdListInfo[] } | null> {
  try {
    const res = await baseRequest<
      WithCommonResult<{
        bd_list: BdListInfo[];
        translate: TranslateMap;
      }>
    >({
      url: `${BASE_URL}/GetBdList`,
      method: 'POST',
      data: {
        from_src: FROM_SRC,
        class_id: classId,
        acescendancy_id: ascendancyId
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return res;
  } catch (error) {
    console.error('fetch bd list failed:', error);
  }
  return null;
}

export interface PassiveSkillStoneItem {
  label: string;
  priority: number;
  items: string[];
}

// BD 详情信息接口
export interface BdDetailInfo {
  bd_id: string;
  class: string;
  ascendancy_class: string;
  title: string;
  author: string;
  tags: string[];
  passive_skill: string;
  skills: string[];
  items: object;
  passive_skill_stone: PassiveSkillStoneItem[];
  output_method: PassiveSkillStoneItem;
  equipment_complex: PassiveSkillStoneItem;
  passive_skill_point: string;
}

/**
 * 查询BD详情
 * @param bdIdList BD ID列表（最多10个）
 * @returns BD详情列表和翻译映射
 */
export async function getBdDetail(
  bdIdList: string[]
): Promise<{ bd_list: BdDetailInfo[] } | null> {
  try {
    // 限制最多查询10个
    const limitedBdIdList = bdIdList.slice(0, 10);
    const res = await baseRequest<
      WithCommonResult<{
        bd_list: BdDetailInfo[];
        translate: TranslateMap;
      }>
    >({
      url: `${BASE_URL}/GetBdDetail`,
      method: 'POST',
      data: {
        from_src: FROM_SRC,
        bd_id_list: limitedBdIdList
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return res;
  } catch (error) {
    console.error('fetch bd detail failed:', error);
  }
  return null;
}
