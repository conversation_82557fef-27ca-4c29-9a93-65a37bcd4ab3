export const FROM_SRC = 'wegame_client';
export const PAGE_NAME = 'poe2_in_game';

// 应用状态常量
export const STAGE = {
  WELCOME: 'welcome',
  ROLE_SELECT: 'role_select',
  ADVANCEMENT: 'advancement',
  OUTLAND_MAP: 'outland_map',
  BOSS_BATTLE: 'boss_battle',
  INVENTORY_PANEL: 'inventory_panel',
  GEM_SKILL_PANEL: 'gem_skill_panel',
  PASSIVE_SKILL_PANEL: 'passive_skill_panel'
} as const;

// OSS 表名常量
export const OSS_TABLE: Record<string, string> = {
  [STAGE.WELCOME]: 'helper_scene_introduce_2025715',
  [STAGE.ROLE_SELECT]: 'helper_scene_role_2025715',
  [STAGE.ADVANCEMENT]: 'helper_scene_introduce_2025715',
  [STAGE.OUTLAND_MAP]: 'helper_scene_introduce_2025715',
  [STAGE.BOSS_BATTLE]: 'helper_scene_boss_2025715',
  [STAGE.INVENTORY_PANEL]: 'helper_scene_introduce_2025715',
  [STAGE.GEM_SKILL_PANEL]: 'helper_scene_introduce_2025715',
  [STAGE.PASSIVE_SKILL_PANEL]: 'helper_scene_introduce_2025715'
} as const;

// 通用型场景化推荐包含的板块(场景介绍)
export const COMMON_STAGES: readonly string[] = [
  STAGE.WELCOME,
  STAGE.OUTLAND_MAP,
  STAGE.ADVANCEMENT,
  STAGE.INVENTORY_PANEL,
  STAGE.GEM_SKILL_PANEL,
  STAGE.PASSIVE_SKILL_PANEL
] as const;

// 需要上报的场景数组(AI对话历史记录)
export const REPORT_SCENES: readonly string[] = [
  STAGE.WELCOME,
  STAGE.ADVANCEMENT,
  STAGE.OUTLAND_MAP,
  STAGE.INVENTORY_PANEL,
  STAGE.GEM_SKILL_PANEL,
  STAGE.PASSIVE_SKILL_PANEL
] as const;

// 场景化记录storage
export const SCENE_ENTRY_RECORD_KEY = 'poe2_scene_entry_record';
// 仅首次触发的场景初始化数据
export const SCENE_ENTRY_RECORD_DEFAULT = {
  [STAGE.WELCOME]: false,
  [STAGE.INVENTORY_PANEL]: false,
  [STAGE.GEM_SKILL_PANEL]: false,
  [STAGE.PASSIVE_SKILL_PANEL]: false
};
