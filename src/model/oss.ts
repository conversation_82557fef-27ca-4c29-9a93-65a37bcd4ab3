import { baseRequest } from '@tencent/wegame-web-sdk';
import { WithCommonResult } from '@src/types/common';

const BATCH_QUERY_OSS_URL = '/api/rail/web/data_filter/game_config/batch_query';

export interface OssRoleItem {
  role_id?: string;
  advanced_role?: string;
  introduce?: string;
  avatar?: string;
  role?: string;
}
export interface OssSceneItem extends OssRoleItem {
  scence_type?: string;
  data_name?: string;
  content?: string;
  title?: string;
  scene_title?: string;
  item_id?: string;
  item_update_time?: string;
  boss_id?: string;
  show_time?: string;
  show_time_text?: string;
}

// 通用 OSS 配置获取场景化内容接口
export async function getOssConfig(tableName: string) {
  try {
    const resp = await baseRequest<
      WithCommonResult<{
        [tableName: string]: OssSceneItem[];
      }>
    >({
      url: BATCH_QUERY_OSS_URL,
      method: 'POST',
      data: {
        command: 'list_all',
        params: [
          {
            data_names: tableName,
            command: 'list_all',
            game_ids: [],
            items_per_pager: 50,
            start_page: 0
          }
        ]
      }
    });
    if (resp.result.error_code) {
      throw new Error(resp.result.error_message);
    }
    return resp;
  } catch (error) {
    console.error('fetch oss config failed', error);
  }
  return null;
}
