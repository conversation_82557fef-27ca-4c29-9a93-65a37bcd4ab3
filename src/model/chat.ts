// pb 协议如下：
/**
service Poe2AI {
    // 用户开启新会话
    rpc NewSession (NewSessionReq) returns (NewSessionRsp) {}
    // 对话能力 sse
    rpc Chat (ChatReq) returns (ChatRsp) {}
    // 会话上报
    rpc SessionReport (SessionReportReq) returns (SessionReportRsp) {}
    // 会话列表
    rpc GetSessionList (GetSessionListReq) returns (GetSessionListRsp) {}
    // 会话列表删除
    rpc DelSessionList (DelSessionListReq) returns (DelSessionListRsp) {}
    // 基于用户数据的问题池推荐
    rpc QuestionRecommend (QuestionRecommendReq) returns (QuestionRecommendRsp) {}
    // 是否灰度用户
    rpc IsGray (IsGrayReq) returns (IsGrayRsp) {}
}

// 角色定义
enum RoleType {
    ROLE_TYPE_USER = 0;         // 用户
    ROLE_TYPE_SYSTEM = 1;       // 系统
    ROLE_TYPE_ASSISTANT = 2;    // 助手
    ROLE_TYPE_OPERATIONS = 3;   // 运营
}

// 用户开启新会话
message NewSessionReq {
    wegame.comm.Head head = 1;
    string from_src  = 2;           // 使用场景，必填，自定义字符串
    string scene = 3;               // 会话场景
}
message NewSessionRsp {
    wegame.comm.Result result = 1;
    string session_id = 2;          // 会话记录id
}

// 直接zip压缩的get请求
message ChatReq {
    string scene = 1;           // 会话场景
    string query = 2;           // 查询问题
    string user_data = 3;       // 用户内容
    string scene_data = 4;      // 场景内容
    string bd_data= 5;          // 推荐db内容
    string session_id = 20;     // 会话记录id
}

// 返回sse数据
message ChatRsp {
    wegame.comm.Result result = 1;
    string reasoning_content = 2;           // 推理内容
    string content = 3;                     // 内容
    string session_id = 4;                  // 会话记录id
    string trace_id = 5;                    // chat的id
    string scene = 6;                       // 会话场景
    repeated string related_questions = 8;  // 相关问题
    string create_time = 9;                 // 创建时间
}


message message {
    string role = 1;        // 角色:user,system,assistant,operations
    string content = 2;     // 内容
}
message SessionInfo {
    string session_id = 1;          // 会话记录id
    string scene = 2;               // 会话场景
    string title = 3;               // 上报标题
    repeated message messages = 4;  // 上报消息

}
// 会话上报
message SessionReportReq {
    wegame.comm.Head head = 1;
    string from_src  = 2;           // 使用场景，必填，自定义字符串
    SessionInfo session_info = 3;   // 会话信息
}
message SessionReportRsp {
    wegame.comm.Result result = 1;
}

// 会话列表
message GetSessionListReq {
    wegame.comm.Head head = 1;
    string from_src  = 2;           // 使用场景，必填，自定义字符串
    map<string, string> filter = 3; // 过滤条件
    uint32 page_size = 4;           // 每页数量
    uint32 page_num = 5;            // 页码
}
message GetSessionListRsp {
    wegame.comm.Result result = 1;
    repeated SessionInfo sessions = 2;
}


// 会话列表删除
message DelSessionListReq {
    wegame.comm.Head head = 1;
    string from_src  = 2;                   // 使用场景，必填，自定义字符串
    repeated string session_id_list = 3;    // 会话记录id
}
message DelSessionListRsp {
    wegame.comm.Result result = 1;
}

// 基于用户数据的问题池推荐
message QuestionRecommendReq {
    wegame.comm.Head head = 1;
    string from_src  = 2;       // 使用场景，必填，自定义字符串
    uint32 count = 3;           // 问题推荐数量
}
message QuestionRecommendRsp {
    wegame.comm.Result result = 1;
    repeated string questions = 2; // 推荐问题
}

// 是否灰度用户
message IsGrayReq {
    wegame.comm.Head head = 1;
    string from_src  = 20;      // 使用场景，必填，自定义字符串
}

message IsGrayRsp {
    wegame.comm.Result result = 1;
    bool is_gray = 2;
}

 */

import { WithCommonResult } from '@src/types/common';
import { baseRequest } from '@tencent/wegame-web-sdk';

export interface SessionInfo {
  session_id: string;
  scene?: string;
  title: string;
  messages?: Message[];
  create_time: number;
}

export enum RoleType {
  ROLE_TYPE_USER = 0,
  ROLE_TYPE_SYSTEM = 1,
  ROLE_TYPE_ASSISTANT = 2,
  ROLE_TYPE_OPERATIONS = 3
}

export interface Message {
  role: 'user' | 'system' | 'assistant' | 'operations';
  content: string;
  reasoning_content?: string;
  create_time: number;
  id: string;
}

export async function newSession() {
  try {
    const res = await baseRequest<
      WithCommonResult<{
        session_id: string;
      }>
    >({
      url: '/api/v1/wegame.pallas.game.Poe2AI/NewSession',
      method: 'POST',
      data: {
        scene: 'common'
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return res.session_id;
  } catch (error) {
    console.error(`create session failed: ${error}`);
    throw error;
  }
}

export async function getSessionList(index?: number, pageSize = 30) {
  try {
    const res = await baseRequest<
      WithCommonResult<{
        sessions: SessionInfo[];
        next_index: number;
      }>
    >({
      url: '/api/v1/wegame.pallas.game.Poe2AI/GetSessionList',
      method: 'POST',
      data: {
        index,
        page_size: pageSize
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return {
      sessions: res.sessions.filter(s => !!s.session_id),
      next_index: res.next_index
    };
  } catch (error) {
    console.error(`fetch session list failed: ${error}`);
  }
  return {
    sessions: []
  };
}

export async function getSessionDetail(
  sessionId: string,
  index?: number,
  pageSize = 10
) {
  try {
    const res = await baseRequest<
      WithCommonResult<{
        session_info: SessionInfo;
        next_index: number;
      }>
    >({
      url: '/api/v1/wegame.pallas.game.Poe2AI/GetSessionDetail',
      method: 'POST',
      data: {
        session_id: sessionId,
        index,
        page_size: pageSize
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return {
      session_info: res.session_info,
      next_index: res.next_index
    };
  } catch (error) {
    console.error(`fetch session info failed: ${error}`);
  }
  return {
    session_info: null
  };
}

export async function getQuestionRecommend(count = 5) {
  try {
    const res = await baseRequest<
      WithCommonResult<{
        questions: string[];
      }>
    >({
      url: '/api/v1/wegame.pallas.game.Poe2AI/QuestionRecommend',
      method: 'POST',
      timeout: 20000,
      data: {
        count
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return res.questions;
  } catch (error) {
    console.error(`fetch question recommend failed: ${error}`);
  }
  return [];
}

export async function isGray() {
  try {
    const res = await baseRequest<
      WithCommonResult<{
        is_gray: boolean;
      }>
    >({
      url: '/api/v1/wegame.pallas.game.Poe2AI/IsGray',
      method: 'POST'
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return res.is_gray;
  } catch (error) {
    console.error(`fetch is gray failed: ${error}`);
  }
  return false;
}

export async function sessionReport(params: {
  role: 'user' | 'system' | 'assistant' | 'operations';
  title: string;
  content: string;
}): Promise<{
  sessionId: string;
  title: string;
}> {
  try {
    const sessionId = await newSession();
    const res = await baseRequest<WithCommonResult>({
      url: '/api/v1/wegame.pallas.game.Poe2AI/SessionReport',
      method: 'POST',
      data: {
        session_info: {
          session_id: sessionId,
          scene: 'operations',
          title: params.title,
          messages: [
            {
              content: params.content,
              role: params.role,
              session_id: sessionId
            }
          ]
        }
      }
    });
    if (res.result.error_code) {
      throw new Error(res.result.error_message);
    }
    return {
      sessionId,
      title: params.title
    };
  } catch (error) {
    console.error(`fetch session report failed: ${error}`);
    throw error;
  }
}
