<template>
  <div v-if="modelValue">
    <div
      class="section-chat"
      :class="[{ showCategory: isOpenChatCategory }, { mini: isMiniChat }]"
    >
      <div class="chat-dialog">
        <div class="chat-tools">
          <!-- 扩大窗口按钮  -->
          <div
            v-if="isMiniChat"
            class="chat-button-expand"
            @click="toggleMiniChat"
          ></div>
          <!-- 收小窗口按钮  -->
          <div
            v-else
            class="chat-button-collapse"
            @click="toggleMiniChat"
          ></div>
          <!-- 关闭按钮  -->
          <div class="chat-button-close" @click="handleClose"></div>
        </div>
        <div class="chat-cont">
          <!-- 标题栏 S  -->
          <div class="chat-cont-title">
            <span class="window-title-logo"></span>
            <span class="window-title-desc">腾讯混元提供支持</span>
          </div>
          <!-- 标题栏 E -->
          <div class="chat-cont-inner">
            <!-- 问答分组栏 S  -->
            <div class="chat-category">
              <div class="chat-category-box">
                <!-- 展开折叠侧边栏按钮 S  -->
                <span
                  class="chat-category-button"
                  @click="toggleChatCategory"
                ></span>
                <!-- 展开折叠侧边栏按钮 E  -->

                <!-- 侧边栏 分组列表 S  -->
                <div
                  ref="sessionListScrollContainer"
                  class="chat-category-inner"
                >
                  <div
                    v-for="item in sessionListByDate"
                    :key="item.date"
                    listChatGroupMock
                    class="chat-group-item"
                  >
                    <div class="chat-group-title">
                      <span>{{ item.date }}</span>
                    </div>
                    <div
                      v-for="subItem in item.list"
                      :key="subItem.session_id"
                      class="chat-title-link"
                      :class="{
                        current: subItem.session_id === currentSessionId
                      }"
                      @click="handleSessionIdUpdate(subItem.session_id)"
                    >
                      <span>{{ subItem.title }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 侧边栏 分组列表 E  -->
            </div>
            <!-- 问答分组栏 E  -->

            <KeepAlive max="10">
              <Session
                :key="currentSessionId"
                :session-id="currentSessionId"
                :init-question="newSessionInitQuestion"
                @update:session-id="handleSessionIdUpdate"
                @new-session-created="handleNewSessionCreated"
              />
            </KeepAlive>
          </div>
        </div>
        <div class="chat-bg">
          <div class="chat-bg-top">
            <span class="chat-bg-top-center"></span>
          </div>
          <div class="chat-bg-main"></div>
          <div class="chat-bg-bottom">
            <span class="chat-bg-bottom-center"></span>
          </div>
          <div class="chat-bg-mask"></div>
        </div>
      </div>
    </div>
    <div v-if="!isMiniChat" class="chat-dialog-mask"></div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue';
import '@src/assets/scss/pages/assistant/chat-ui.scss';
import Session from './session.vue';
import { isShowChat } from '@src/composable/aiChatStage';
import { getSessionList, SessionInfo } from '@src/model/chat';
import { groupBy } from '@src/util/array';
import dayjs from 'dayjs';
import { useInfiniteScroll } from '@vueuse/core';
import { emitter } from '@src/util/event';

const emits = defineEmits(['update:modelValue', 'expanded', 'closed']);
// 组件属性
withDefaults(
  defineProps<{
    modelValue: boolean;
  }>(),
  {
    modelValue: false
  }
);

const currentSessionId = ref('');

const newSessionInitQuestion = ref('');

const insertedSessionList = ref<SessionInfo[]>([]);

const sessionListScrollContainer = ref<HTMLElement>();

// const { data: histroySessionList } = useAsync(() => getSessionList());

const histroySessionList = ref<SessionInfo[]>([]);

let sessionListPaginationIndex: number | undefined = undefined;

let loadingSessionList = false;
const lastPage = ref(false);

const loadSessionList = async () => {
  if (loadingSessionList || lastPage.value) {
    return;
  }
  loadingSessionList = true;
  const { sessions, next_index: nextIndex } = await getSessionList(
    sessionListPaginationIndex
  );
  loadingSessionList = false;
  if (sessions.length) {
    histroySessionList.value = [...histroySessionList.value, ...sessions];
  } else {
    lastPage.value = true;
  }
  sessionListPaginationIndex = nextIndex;
};

useInfiniteScroll(sessionListScrollContainer, loadSessionList, {
  distance: 20,
  canLoadMore: () => !lastPage.value
});

const sessionListByDate = computed(() => {
  if (!histroySessionList.value) {
    return [];
  }
  const sessionList = [
    ...insertedSessionList.value,
    ...(histroySessionList.value ?? [])
  ];
  const sessionGroupByDate = groupBy(sessionList, session => {
    return dayjs(session.create_time * 1000).format('YYYY-MM-DD');
  });

  return Object.keys(sessionGroupByDate)
    .map(key => {
      return {
        date: key,
        list: sessionGroupByDate[key]
      };
    })
    .sort((a, b) => b.date.localeCompare(a.date));
});

const isMiniChat = ref(true);
const isOpenChatCategory = ref(false);

const handleClose = () => {
  emits('update:modelValue', false);
  emits('closed');
  emits('expanded', false);
  isMiniChat.value = true;
  isOpenChatCategory.value = false;
  newSessionInitQuestion.value = '';
};

const toggleChatCategory = () => {
  isOpenChatCategory.value = !isOpenChatCategory.value;
};

const toggleMiniChat = () => {
  if (isMiniChat.value) {
    isMiniChat.value = false;
    isOpenChatCategory.value = true;
    emits('expanded', true);
  } else {
    isMiniChat.value = true;
    isOpenChatCategory.value = false;
    emits('expanded', false);
  }
};

watch(isShowChat, newVal => {
  if (!newVal) {
    handleClose();
  }
});

const handleSessionIdUpdate = (sessionId: string) => {
  newSessionInitQuestion.value = '';
  currentSessionId.value = sessionId;
};

const handleNewSessionCreated = (payload: {
  sessionId: string;
  role: 'user' | 'system' | 'assistant' | 'operations';
  initQuestion: string;
}) => {
  currentSessionId.value = payload.sessionId;
  newSessionInitQuestion.value = payload.initQuestion;

  insertedSessionList.value.unshift({
    session_id: payload.sessionId,
    title: payload.initQuestion.slice(0, 20),
    create_time: Date.now() / 1000
  });
};

emitter.on('new-session-created', ({ sessionId, title }) => {
  insertedSessionList.value.unshift({
    session_id: sessionId,
    title,
    create_time: Date.now() / 1000
  });
});

onMounted(() => {
  loadSessionList();
});
</script>
