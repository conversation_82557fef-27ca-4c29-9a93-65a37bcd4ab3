<template>
  <div v-if="modelValue" class="section-guide" :class="{ show: modelValue }">
    <!-- 关闭 S  -->
    <div
      class="button-guide-close"
      @click="$emit('update:modelValue', false)"
    ></div>
    <!-- 关闭 E  -->

    <div class="section-guide-cont">
      <!-- 通用型场景化推荐内容 -->
      <div
        v-if="COMMON_STAGES.includes(stage) || stage === STAGE.BOSS_BATTLE"
        class="section-tips-words"
      >
        <h3 v-if="sceneOssData.scene_title" class="guide-tips-title">
          {{ sceneOssData.scene_title }}
        </h3>
        <div class="guide-tips-cont">
          <p v-html="sceneOssData.content"></p>
        </div>
      </div>

      <!-- 升华的职业介绍 S -->
      <div v-if="props.stage === STAGE.ROLE_SELECT" class="section-role-select">
        <h3 class="guide-tips-title">
          {{ roleSelectOssData?.[0]?.role }}后续可升华的职业
        </h3>
        <div class="guide-tips-cont">
          <div
            v-for="(item, index) in roleSelectOssData"
            :key="index"
            class="role-select-item"
          >
            <div class="role-select-avatar">
              <img :src="item.avatar" alt="" />
            </div>
            <div class="role-select-cont">
              <div class="role-select-title">
                {{ item.advanced_role }}
              </div>
              <div class="role-select-describition">
                {{ item.introduce }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 升华的职业介绍 E -->

      <!-- 自动关闭提示 -->
      <AutoCloseTip
        ref="autoCloseTipRef"
        :has-text="!!Number(sceneOssData.show_time_text) || false"
        :auto-close-time="Number(sceneOssData.show_time) || 15000"
        @close="$emit('update:modelValue')"
      />
    </div>
    <!-- 背景UI S  -->
    <div class="section-guide-bg"><span></span></div>
    <!-- 背景UI E  -->
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import AutoCloseTip from '@src/components/auto-close-tip.vue';
import type { OssSceneItem, OssRoleItem } from '@src/model/oss';
import { STAGE, COMMON_STAGES, REPORT_SCENES } from '@src/model/common';
import { useReport } from '@src/composable/report';

defineEmits(['update:modelValue']);
// 组件属性
const props = withDefaults(
  defineProps<{
    modelValue: boolean;
    stage: string;
    sceneOssData: Partial<OssSceneItem>;
    roleSelectOssData: OssRoleItem[];
  }>(),
  {
    modelValue: false
  }
);

const { sceneReport } = useReport();

const autoCloseTipRef = ref<InstanceType<typeof AutoCloseTip> | null>(null);
// 监听 stage 变化，进行数据上报和重启定时器
watch(
  () => ({ stage: props.stage, modelValue: props.modelValue }),
  ({ stage, modelValue }) => {
    if (modelValue && stage) {
      // 数据上报
      if (props.sceneOssData.content && REPORT_SCENES.includes(stage)) {
        sceneReport(props.sceneOssData.title || '', props.sceneOssData.content);
      }
      // 重启定时器
      if (autoCloseTipRef.value) autoCloseTipRef.value.startTimer();
    }
  }
);
</script>
