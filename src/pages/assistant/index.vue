<template>
  <div class="page-frame page-frame--guide">
    <!-- DDJ 头像状态枚举  -->
    <!-- 首次进入探头: stage01 -->
    <!-- 进入后常态:   stage02 -->
    <!-- 自动展开提示: stage03 -->
    <!-- 主动展开对话: stage04 -->
    <!-- 对话框扩展开: stage05 -->
    <div
      class="section-ddj"
      :class="[
        { stage01: isNewEnter },
        { stage02: !isNewEnter },
        { stage03: isShowGuide },
        { stage04: isShowChat },
        { stage05: isChatExpanded }
      ]"
    >
      <span class="section-ddj-avatar" @click="handleShowChat"></span>
      <span class="section-ddj-lit"></span>
      <span class="section-ddj-light"></span>
      <span class="section-ddj-name"></span>
    </div>
    <!-- DDJ 头像 E  -->

    <!-- 场景功能提示推荐 S  -->
    <GuideTips
      v-model="isShowGuide"
      :stage="props.stage"
      :scene-oss-data="sceneOssData"
      :role-select-oss-data="roleSelectOssData"
    />
    <!-- 场景功能提示推荐 E  -->

    <ChatPanel
      v-model="isShowChat"
      @expanded="handleChatExpanded"
      @closed="handleChatClosed"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import '@src/assets/scss/pages/assistant/index.scss';
import GuideTips from './guide-tips.vue';
import ChatPanel from './chat-panel.vue';
import { isShowChat } from '@src/composable/aiChatStage';
import { useShareOss } from '@src/composable/oss';
import type { OssSceneItem, OssRoleItem } from '@src/model/oss';
import { STAGE } from '@src/model/common';

const emits = defineEmits(['showChat']);

const props = defineProps<{
  stage: string;
  isNew: boolean;
  bossName: string;
  roleId: number | null;
}>();

const { getSceneOssData, getRoleSelectOssData } = useShareOss();

const isNewEnter = ref(props.isNew);
const isShowGuide = ref(false);
const isChatExpanded = ref(false);

const sceneOssData = ref<Partial<OssSceneItem>>({});
const roleSelectOssData = ref<OssRoleItem[]>([]);

const handleShowChat = () => {
  isShowGuide.value = false;
  isShowChat.value = !isShowChat.value;
  isNewEnter.value = false;
};

const handleChatExpanded = (e: boolean) => {
  isChatExpanded.value = e;
};

const handleChatClosed = () => {
  isChatExpanded.value = false;
};

watch(isShowChat, newVal => {
  emits('showChat', newVal);
});

// 监听 stage 变化，控制显示状态
watch(
  () => ({
    stage: props.stage,
    bossName: props.bossName,
    roleId: props.roleId
  }),
  async ({ stage, bossName, roleId }) => {
    sceneOssData.value = {};
    roleSelectOssData.value = [];
    isShowGuide.value = false;
    if (stage === STAGE.ROLE_SELECT && roleId) {
      roleSelectOssData.value = await getRoleSelectOssData(stage, roleId);
    } else {
      sceneOssData.value = await getSceneOssData(stage, bossName);
    }
    if (
      stage &&
      (sceneOssData.value.content || roleSelectOssData.value.length)
    ) {
      isShowChat.value = false;
      isShowGuide.value = true;
    }
  },
  { immediate: true }
);

// const setEvenFontSize = (element : HTMLElement) => {
//   const viewportWidth = window.innerWidth;
//   const calculatedSize = (viewportWidth / 1920) * 100;
//   // 四舍五入到最近的偶数
//   const evenSize = Math.round(calculatedSize / 2) * 2;
//   element.style.fontSize = evenSize + 'px';
// }

// onMounted(() => {
//   setEvenFontSize(document.documentElement);
//   // 监听窗口大小变化
//   window.addEventListener('resize', () => {
//     setEvenFontSize(document.documentElement);
//   });
// });

// onUnmounted(() => {
//   window.removeEventListener('resize', () => {
//     setEvenFontSize(document.documentElement);
//   });
// });
</script>
