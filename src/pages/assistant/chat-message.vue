<!-- eslint-disable vue/no-v-html -->
<template>
  <!-- 消息日期时间 S  -->
  <!-- <div v-if="showDate" class="chat-message-date"> -->
  <!--   <span>2025-06-03 12:35</span> -->
  <!-- </div> -->
  <!-- 消息日期时间 E  -->

  <!-- 消息 Loading  S -->
  <div v-if="thinking" class="chat-message-row">
    <div class="chat-loading">
      <span class="chat-loading-icon"></span>
      <span class="chat-loading-icon"></span>
      <span class="chat-loading-icon"></span>
    </div>
  </div>
  <!-- 消息 Loading  E -->

  <div v-else-if="!isReply" class="chat-message-row message-mine">
    <div class="chat-message-item">
      <div class="chat-message-words">{{ message.content }}</div>
    </div>
  </div>

  <!-- 场景触发  -->
  <div
    v-else-if="message.role === 'operations'"
    class="chat-message-row message-reply operations"
  >
    <div class="chat-message-item">
      <div class="chat-message-words" v-html="message.content"></div>
    </div>
  </div>

  <div v-else ref="replyEl" class="chat-message-row message-reply">
    <div class="chat-message-item">
      <!-- 收起思考信息加collapse类,展开expand-->
      <div
        v-if="showThinking"
        class="chat-thinking"
        :class="`${expandThinking ? 'expand' : 'collapse'}`"
      >
        <div v-if="thinking" class="chat-loading">
          <span class="chat-loading-icon"></span>
          <span class="chat-loading-icon"></span>
          <span class="chat-loading-icon"></span>
        </div>
        <div class="chat-thinking-tit" @click="toggleThinking">{{
          message.reasoning_content && !message.content ? '思考中' : '思考完毕'
        }}</div>
        <div
          v-if="parsedReasoningContent"
          class="chat-thinking-content analysis-markdown"
          v-html="parsedReasoningContent"
        >
        </div>
      </div>
      <div
        v-if="parsedContent"
        class="analysis-markdown"
        v-html="parsedContent"
      >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref, watchEffect } from 'vue';
import { marked } from 'marked';
import mermaid from 'mermaid';
import DOMPurify from 'dompurify';
import { Message } from '@src/model/chat';

// 配置Markdown解析选项
marked.setOptions({
  breaks: true // 自动换行
});

mermaid.initialize({ startOnLoad: false });

marked.use({
  renderer: {
    code(code) {
      if (code.lang === 'mermaid') {
        return `<pre class="mermaid">${code.text}</pre>`;
      }
      return `<pre><code>${code.raw}</code></pre>`;
    },
    link(code) {
      return `<a data-href="${code.href}">${code.text}</a>`;
    }
  }
});

const props = defineProps<{
  message: Message & {
    error?: Error | null;
  };
}>();

const replyEl = ref<HTMLElement>();

// const showDate = computed(() => false);

const isReply = computed(() => props.message.role !== 'user');

const thinking = computed(
  () =>
    isReply.value &&
    !props.message.error &&
    !props.message.reasoning_content &&
    !props.message.content
);

const showThinking = computed(
  () => isReply.value && (thinking.value || props.message.reasoning_content)
);

const parsedReasoningContent = ref('');
const parsedContent = ref('');

const expandThinking = ref(true);

// watchEffect(() => {
//   if (isReply.value && props.message.reasoningContent) {
//     expandThinking.value = true;
//   }
// });

watchEffect(() => {
  if (props.message.role === 'user' || !props.message.reasoning_content) {
    return;
  }
  const pendingReasingContent = props.message.reasoning_content;
  requestAnimationFrame(async () => {
    parsedReasoningContent.value = DOMPurify.sanitize(
      await marked.parse(pendingReasingContent)
    );
  });
});

watchEffect(() => {
  if (props.message.role === 'user' || !props.message.content) {
    return;
  }
  const pendingContent = props.message.content;
  requestAnimationFrame(async () => {
    parsedContent.value = DOMPurify.sanitize(
      await marked.parse(pendingContent)
    ).replace(/data-href="(.*?)"/g, 'href="$1" target="_blank"');
    nextTick(() => {
      mermaid.run();
    });
  });
});

const toggleThinking = () => {
  expandThinking.value = !expandThinking.value;
};
</script>
