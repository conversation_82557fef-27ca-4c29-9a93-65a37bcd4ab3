<template>
  <div class="chat-main">
    <!-- 消息互动栏--输入 S -->
    <div ref="scrollContainer" class="chat-messages">
      <div class="chat-message-wrap">
        <div class="chat-message-load">
          <!-- 展开历史 S  -->
          <!-- <div -->
          <!--   v-if="showLoadOlderBtn" -->
          <!--   class="chat-message-load-more" -->
          <!--   @click="loadHistoryMessages" -->
          <!-- > -->
          <!--   <span>展开历史对话</span> -->
          <!-- </div> -->
          <!-- 展开历史 E  -->
        </div>
        <div class="chat-message-list">
          <div
            v-if="!sessionId && recommendQuestions.length"
            class="message-menu-content"
          >
            <div class="message-menu-head">
              <div class="message-menu-title">猜你想问</div>
              <div class="button-refresh" @click="refreshSuggestQuestions"
                >换一批</div
              >
            </div>
            <div class="message-menu-list">
              <div
                v-for="item in recommendQuestions"
                :key="item"
                class="message-menu-link"
                @click="sendMessage(item)"
              >
                <div class="message-menu-link-text">
                  {{ item }}
                </div>
              </div>
            </div>
          </div>

          <ChatMessage
            v-for="message in messages"
            :key="message.id"
            :message="message"
          />

          <!-- 消息 Loading  S -->
          <div v-if="replyError" class="chat-message-row">
            <div class="chat-nodata network">
              <span class="chat-nodata-icon"></span>
              <p class="chat-nodata-text"> 网络去哪了？稍后再试试吧！ </p>
            </div>
          </div>
          <!-- 消息 Loading  E -->
        </div>
      </div>
    </div>
    <!-- 消息互动栏--输入 E -->

    <!-- 底栏--输入 S -->
    <div class="chat-actions">
      <div class="chat-action-tools">
        <div class="chat-tools-main">
          <div
            v-for="item in relatedQuestions"
            :key="item"
            class="chat-label-item"
            @click="sendMessage(item)"
          >
            <span>{{ item }}</span>
          </div>
        </div>
        <!-- 新建会话按钮 S  -->
        <div class="chat-button-new" @click="createNewEmptySession">
          <span class="chat-button-new-icon"></span>
          <span class="chat-button-new-text">开启新对话</span>
        </div>
        <!-- 新建会话按钮 E  -->
      </div>
      <div class="chat-textarea">
        <div class="chat-textarea-ele">
          <!-- 输入表单 S  -->
          <textarea
            id="textarea-dom"
            ref="inputEl"
            v-model="inputQuery"
            placeholder="请输入"
            spellcheck="false"
            :readonly="!enableSendMessage"
            @keydown.enter="sendMessage()"
          ></textarea>
          <span class="textarea-border"></span>
          <!-- 输入表单 E  -->
          <div class="chat-textarea-actions">
            <!-- 发送按钮事件 S  -->
            <div class="chat-button-send" @click="sendMessage()"></div>
            <!-- 发送按钮事件 E  -->
          </div>
        </div>
      </div>
      <!-- 底栏--输入 E -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import '@src/assets/scss/pages/assistant/message-list.scss';
import ChatMessage from './chat-message.vue';
import {
  getSessionDetail,
  getQuestionRecommend,
  Message,
  newSession
} from '@src/model/chat';
import {
  computed,
  nextTick,
  onActivated,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  shallowRef
} from 'vue';
import { useSSE } from '@src/composable/sse';
import { useEventListener, useInfiniteScroll } from '@vueuse/core';
const listSuggestMock = [
  '我的职业玩【闪电箭】最佳BD是什么？',
  '技能【毒雨】和【腐蚀箭】哪一个更适合开荒？相关搭配是什么？',
  '前10小时最优开荒流程？优先做主线还是支线？',
  '新版天赋树怎么点？我的职业玩【闪电箭】最佳天赋路径是什么？',
  'BOSS转阶段时我的DPS骤降，是技能循环问题还是词缀缺陷？'
];

const props = defineProps<{
  sessionId?: string;
  initQuestion?: string;
}>();

const emits = defineEmits<{
  (
    e: 'new-session-created',
    payload: {
      sessionId: string;
      role: 'user' | 'system' | 'assistant' | 'operations';
      initQuestion: string;
    }
  ): void;
  (e: 'update:session-id', sessionId: string): void;
}>();

let autoScroll = true;

let sseCloseHandler: (() => void) | null = null;

const scrollContainer = ref<HTMLElement>();

const recommendQuestions = ref<string[]>([]);

const historyMessages = ref<Message[]>([]);

const appendedMessages = ref<Message[]>([]);

const relatedQuestions = ref<string[]>([]);

const inputEl = ref<HTMLTextAreaElement>();

const inputQuery = ref('');

let messagesPaginationIndex: number | undefined = undefined;

let loadingMessages = false;
const lastPage = ref(false);

const enableSendMessage = ref(true);

const replyError = shallowRef<Error | null>(null);

const messages = computed(() =>
  appendedMessages.value.concat(historyMessages.value)
);

// const showLoadOlderBtn = computed(
//   () => historyMessages.value.length && !lastPage.value
// );

const scrollToBottom = () => {
  scrollContainer.value?.scrollTo(0, scrollContainer.value.scrollHeight);
};

const sendMessage = async (query?: string) => {
  if (!enableSendMessage.value || (!query && !inputQuery.value)) {
    return;
  }
  if (!props.sessionId) {
    const newSessionId = await newSession();
    emits('new-session-created', {
      sessionId: newSessionId,
      role: 'user',
      initQuestion: query || inputQuery.value
    });
    return;
  }
  replyError.value = null;
  enableSendMessage.value = false;
  relatedQuestions.value = [];
  appendedMessages.value.unshift({
    id: Date.now().toString(), // 本地临时id用本地时间戳
    role: 'user',
    content: query || inputQuery.value,
    create_time: Date.now()
  });
  const replyMessage = reactive<
    Message & {
      error: Error | null;
    }
  >({
    id: '',
    content: '',
    reasoning_content: '',
    role: 'assistant',
    create_time: 0,
    error: null
  });
  const { sessionId } = props;
  const { close } = useSSE(
    '/api/sse/wegame.pallas.game.Poe2AI/Chat',
    {
      query: query || inputQuery.value,
      session_id: sessionId,
      count: 3 // 返回推荐问题数量
    },
    {
      onOpen() {
        autoScroll = true;
      },
      onMessage({
        content,
        reasoning_content,
        create_time,
        id,
        related_questions
      }) {
        enableSendMessage.value = false;
        replyMessage.id = id;
        replyMessage.error = null;
        replyMessage.content = content;
        replyMessage.reasoning_content = reasoning_content;
        replyMessage.create_time = create_time;
        if (!scrollContainer.value) {
          return;
        }
        if (autoScroll) {
          nextTick(() => {
            scrollToBottom();
          });
        } else if (
          scrollContainer.value.scrollTop +
            scrollContainer.value.clientHeight >=
          scrollContainer.value.scrollHeight - 10
        ) {
          // 如果当前滚动条到底部，则自动滚动
          autoScroll = true;
        }
        if (related_questions?.length) {
          relatedQuestions.value = related_questions;
        }
      },
      onDone() {
        enableSendMessage.value = true;
        sseCloseHandler = null;
      },
      onClose() {
        enableSendMessage.value = true;
        sseCloseHandler = null;
      },
      onError(e) {
        replyError.value = e;
        replyMessage.error = e;
        enableSendMessage.value = true;
        sseCloseHandler = null;
      }
    }
  );
  sseCloseHandler = close;
  appendedMessages.value.unshift(replyMessage);
  if (!query) {
    setTimeout(() => {
      inputQuery.value = '';
    }, 50);
  }
  nextTick(() => {
    scrollToBottom();
  });
};

const handleWheel = (e: WheelEvent) => {
  if (e.deltaY < 0) {
    // 如果用户操作滚轮向上滚动，则不自动滚动
    autoScroll = false;
  }
};

useEventListener(scrollContainer, 'wheel', handleWheel);

const loadHistoryMessages = async () => {
  if (!props.sessionId || loadingMessages || lastPage.value) {
    return;
  }
  loadingMessages = true;
  const sessionDetail = await getSessionDetail(
    props.sessionId,
    messagesPaginationIndex
  );
  loadingMessages = false;
  if (sessionDetail?.session_info?.messages?.length) {
    if (!historyMessages.value.length) {
      // 如果是第一次加载，滚动到最底部
      setTimeout(() => {
        scrollToBottom();
      }, 50);
    }
    historyMessages.value = [
      ...historyMessages.value,
      ...[...(sessionDetail?.session_info.messages || [])]
    ];
  } else {
    lastPage.value = true;
  }
  messagesPaginationIndex = sessionDetail?.next_index;
};

useInfiniteScroll(scrollContainer, loadHistoryMessages, {
  distance: 600,
  direction: 'top',
  canLoadMore: () => !!props.sessionId && !lastPage.value
});

const createNewEmptySession = () => {
  emits('update:session-id', '');
};

const refreshSuggestQuestions = async () => {
  recommendQuestions.value = (await getQuestionRecommend()).slice(0, 5);
  // TODO: 删除下面mock逻辑
  if (!recommendQuestions.value.length) {
    recommendQuestions.value = listSuggestMock;
  }
};

onMounted(() => {
  if (!props.sessionId) {
    // 新会话
    refreshSuggestQuestions();
    lastPage.value = true;
  }
  if (props.initQuestion) {
    lastPage.value = true;
    sendMessage(props.initQuestion);
  } else {
    loadHistoryMessages();
  }
});

onActivated(() => {
  scrollToBottom();
});

onBeforeUnmount(() => {
  sseCloseHandler?.();
});
</script>
