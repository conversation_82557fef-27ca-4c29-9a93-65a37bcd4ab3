<template>
  <PanelController
    v-model:show="isShow"
    panel-type="equipment"
    panel-name="inventory_panel"
  >
    <div class="page-frame-cont">
      <!-- 装备窗口  S  -->
      <div class="equipment-panel-wrap">
        <div v-if="isEquipmentShow" class="bd-panel equipment-panel">
          <div class="eq-panel-hd">
            <div class="eq-panel-tit"></div>

            <!-- 关闭按钮 S -->
            <div class="bd-panel-close" @click="handleEquipmentClose"></div>
            <!-- 关闭按钮 E -->
          </div>
          <div class="eq-panel-bd">
            <PlannerSelect v-model:bd-id="plannerId" :list="bdList" />
            <div class="equipment-box-wrap">
              <div class="equipment-box">
                <div class="equipment-box-slot">
                  <!-- 头冠 -->
                  <!-- 
                   传奇:  slot-level-1
                   稀有:  slot-level-2
                   魔法:  slot-level-3 -->
                  <div
                    class="equipment-slot slot-helmet slot-level-2"
                    data-level="2"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Armours/Helmets/Basetypes/HelmetInt05.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <!-- 主服装装备 -->
                  <div
                    class="equipment-slot slot-body-armour slot-level-1"
                    data-level="1"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Armours/BodyArmours/Basetypes/BodyInt03.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <!-- 腰带 -->
                  <div
                    class="equipment-slot slot-belt slot-level-2"
                    data-level="2"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Belts/Uniques/ShavronnesSatchel.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <!-- 手套 -->
                  <div
                    class="equipment-slot slot-gloves slot-level-2"
                    data-level="2"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Armours/Gloves/Basetypes/GlovesInt05.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <!-- 鞋套 -->
                  <div
                    class="equipment-slot slot-boots slot-level-2"
                    data-level="2"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Armours/Boots/Basetypes/BootsInt05.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <!-- 戒指-左 -->
                  <div
                    class="equipment-slot slot-ring-left slot-level-0"
                    data-level="0"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Rings/Basetypes/PrismaticRing.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <!-- 戒指-右 -->
                  <div
                    class="equipment-slot slot-ring-right slot-level-3"
                    data-level="3"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Rings/Basetypes/UnsetRing.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <!-- 护身符-右 -->
                  <div
                    class="equipment-slot slot-amulet slot-level-3"
                    data-level="3"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Amulets/Basetypes/GoldAmulet.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>

                  <!-- 主要武器-左 -->
                  <div
                    class="equipment-slot slot-weapons-left slot-level-1"
                    data-level="1"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <!-- current 选中态 -->
                    <!-- 主按钮  -->
                    <div class="equipment-tab-main current"></div>
                    <!-- 副按钮  -->
                    <div class="equipment-tab-secondary"></div>
                    <!-- 内容体  -->
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Weapons/TwoHandWeapons/WarStaves/Warstaff03.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>

                  <!-- 主要武器-右 -->
                  <div
                    class="equipment-slot slot-weapons-right slot-level-2"
                    data-level="2"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <!-- current 选中态 -->
                    <div class="equipment-tab-main current"></div>
                    <div class="equipment-tab-secondary"></div>
                    <!-- 内容体  -->
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Offhand/Shields/Uniques/redbladebanner.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>

                  <div
                    class="equipment-slot slot-charms slot-level-3"
                    data-level="3"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-slot slot-charm-1">
                      <div class="equipment-source">
                        <img
                          src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Charms/Basetypes/StoneCharm.avif"
                          alt=""
                          role="presentation"
                        />
                      </div>
                    </div>
                    <div
                      class="equipment-slot slot-charm-2 slot-level-2"
                      data-level="2"
                      @mouseenter="handleWeaponMouseEnter"
                      @mouseleave="handleWeaponMouseLeave"
                    >
                      <div class="equipment-source">
                        <img
                          src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Charms/Basetypes/ThawingCharm.avif"
                          alt=""
                          role="presentation"
                        />
                      </div>
                    </div>
                    <div
                      class="equipment-slot slot-charm-3 slot-level-1"
                      data-level="1"
                      @mouseenter="handleWeaponMouseEnter"
                      @mouseleave="handleWeaponMouseLeave"
                    >
                      <div class="equipment-source">
                        <img
                          src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Charms/Basetypes/SilverCharm.avif"
                          alt=""
                          role="presentation"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    class="equipment-slot slot-life-flask slot-level-1"
                    data-level="1"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Flasks/Basetypes/FlaskLife09.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                  <div
                    class="equipment-slot slot-mana-flask slot-level-1"
                    data-level="1"
                    @mouseenter="handleWeaponMouseEnter"
                    @mouseleave="handleWeaponMouseLeave"
                  >
                    <div class="equipment-source">
                      <img
                        src="https://cdn.mobalytics.gg/assets/poe-2/images/game/Art/2DItems/Flasks/Basetypes/FlaskMana09.avif"
                        alt=""
                        role="presentation"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="equipment-box-tips">
                <h3 class="equipment-tips-title">综合词条建议</h3>
                <div class="equipment-tips-group tips-level-1">
                  <div class="eq-tip-title">首选</div>
                  <div class="eq-tip-desc">法术技能等级 I 暴击 I 冰冷伤害</div>
                </div>
                <div class="equipment-tips-group tips-level-2">
                  <div class="eq-tip-title">次选</div>
                  <div class="eq-tip-desc">
                    暴击伤害 I 能量护盾 I 精魂 I 生命值 I 魔力回复 I 魔力值
                  </div>
                </div>
                <div class="equipment-tips-group tips-level-3">
                  <div class="eq-tip-title">可选</div>
                  <div class="eq-tip-desc">抗性</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 装备窗口  E  -->

      <!-- AI分析窗口  S  -->
      <div v-if="isAiShow" class="analysis-panel equipment-analysis-panel">
        <div class="analysis-hd">
          <!-- 关闭按钮 S -->
          <div class="bd-panel-close" @click="handleAiClose"></div>
          <!-- 关闭按钮 E -->
          <div class="analysis-title-wrap">
            <div class="analysis-title"></div>
            <div class="analysis-subtitle"></div>
          </div>
          <div class="analysis-desc">腾讯混元提供支持</div>
        </div>
        <div class="analysis-bd">
          <div class="analysis-markdown bd-analysis-markdown">
            <h2>极寒权杖</h2>
            <h3>装备价值评分 8.7/10</h3>
            <p>
              评估：这件【极寒权杖】目前在交易市场中价值2个崇高石;
              <br />
              建议：可继续赌出精魂词缀;
            </p>
            <h3>个人BD适配分析</h3>
            <p>
              建议您选择含有法术技能伤害提升的装备。同时本章节以冰冻伤害为主，建议您选取含有冰抗的装备，如XXXXX护符，总体冰抗建议≥50%。
            </p>
          </div>
        </div>
      </div>
      <!-- AI分析窗口  E  -->

      <!-- 默认等级: card-level-0 -->
      <!-- 传奇等级: card-level-1 -->
      <!-- 稀有等级: card-level-2 -->
      <!-- 魔法等级: card-level-3 -->
      <div
        v-if="isShowCardTips"
        :style="cardTipsPosition"
        :class="`equipment-card card-level-${cardLevel}`"
      >
        <div class="equipment-card-inner">
          <div class="equipment-card-title">
            <div class="equipment-card-title-text">任意法杖</div>
            <div class="equipment-card-title-bg"></div>
          </div>
          <div class="equipment-card-cont">
            <div class="equipment-card-tips-group">
              <p class="tips-level-item tips-level-1">法术技能等级</p>
              <p class="tips-level-item tips-level-1">冰冷伤害</p>
            </div>
            <div class="equipment-card-tips-group">
              <p class="tips-level-item tips-level-2">额外冰冷伤害</p>
              <p class="tips-level-item tips-level-2">冰冷技能等级</p>
              <p class="tips-level-item">
                <strong class="tips-level-5">施放</strong
                ><strong class="tips-level-3">速度</strong>
              </p>
            </div>
            <div class="equipment-card-tips-group">
              <p class="tips-level-item tips-level-3">额外冰冷伤害</p>
              <p class="tips-level-item tips-level-3">魔力回复率</p>
              <p class="tips-level-item tips-level-4">暴击伤害</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelController>
</template>
<script lang="ts" setup>
import { ref, computed, watchEffect } from 'vue';
import { useAsyncState } from '@vueuse/core';
import '@src/assets/scss/pages/equipment/index.scss';

import PlannerSelect from '@src/components/planner-select.vue';
import PanelController from '@src/components/panel-controller.vue';
import { useBd } from '@src/composable/bd';

const { bdList, fetchBdList } = useBd();

// TODO从客户端获取用户职业ID
const currentClassId = ref('1');
const currentAscendancyId = ref('1');

const plannerId = ref<string>('');

useAsyncState(
  fetchBdList(currentClassId.value, currentAscendancyId.value),
  null,
  { immediate: true }
);

const isShowCardTips = ref(false);

const cardTipsPosX = ref(0);
const cardTipsPosY = ref(0);

const cardTipsPosition = computed(() => {
  return {
    position: 'fixed' as const,
    left: `${cardTipsPosX.value}px`,
    top: `${cardTipsPosY.value}px`
  };
});

const cardLevel = ref('1');

const handleWeaponMouseEnter = (e: any) => {
  const ele = e.target as HTMLElement;

  // @ts-ignore
  cardLevel.value = ele.attributes['data-level'].value;

  const offsetX = ele.getBoundingClientRect().x;
  const offsetY = ele.getBoundingClientRect().y + ele.offsetHeight;

  cardTipsPosX.value = offsetX;
  cardTipsPosY.value = offsetY;
  isShowCardTips.value = true;
};

const handleWeaponMouseLeave = () => {
  isShowCardTips.value = false;
};

const isShow = ref(false);

const isEquipmentShow = ref(true);
const isAiShow = ref(true);
const handleEquipmentClose = () => {
  isEquipmentShow.value = false;
};

const handleAiClose = () => {
  isAiShow.value = false;
};

watchEffect(() => {
  if (!isEquipmentShow.value && !isAiShow.value) {
    isShow.value = false;
  }
});
</script>
