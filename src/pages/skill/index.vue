<template>
  <PanelController
    v-model:show="isSkillShow"
    panel-type="skill"
    panel-name="gem_skill_panel"
  >
    <!-- 主面板 -->
    <div class="bd-panel skill-panel">
      <div class="skill-panel-hd">
        <div class="skill-panel-tit"></div>
        <div class="bd-panel-close" @click="hideMainPanel"></div>
      </div>
      <div class="skill-panel-bd">
        <PlannerSelect v-model:bd-id="bdId" :list="bdList" />
        <div v-if="!nodata" class="skill-planner-main">
          <div class="planner-author">
            <div class="author-avatar">
              <img :src="authorAvatar" alt="" />
            </div>
            <div class="author-info">
              <div class="author-main">
                <div class="author-name">方案作者名字</div>
                <!-- bilibili/tiktok/caimogu -->
                <div class="author-channel-icon bilibili"></div>
              </div>
              <div class="planner-time">
                <span>发布于 2025.05.12</span>
                <span>更新于 2025.06.01</span>
              </div>
            </div>
          </div>
          <div class="planner-skill-key">
            <div class="skill-key-item">
              <div class="skill-key-tit">攻坚手法</div>
              <div class="skill-key-progress">
                <span>冰墙</span>
                <span>冰冻时施放</span>
                <span>暴击时施放</span>
                <span>霜暴</span>
                <span>冰霜新星</span>
              </div>
            </div>
            <div class="skill-key-item">
              <div class="skill-key-tit">刷图手法</div>
              <div class="skill-key-progress">
                <span>冰霜新星</span>
                <span>冰冻时施放</span>
                <span>暴击时施放</span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!nodata" class="skill-planner-detail">
          <div
            v-for="(item, index) in testProgressList"
            :key="index"
            class="skill-detail-item"
          >
            <div class="skill-detail-main">
              <div class="skill-detail-main-icon">
                <img :src="item.poster" alt="" />
              </div>
              <div class="skill-detail-main-tit">{{ item.name }}</div>
            </div>
            <div class="skill-detail-gems">
              <div class="skill-detail-gem">
                <img :src="item.gem" alt="" />
              </div>
              <div class="skill-detail-gem-chain">
                <div
                  v-for="(iitem, iindex) in item.subGems"
                  :key="iindex"
                  class="skill-detail-subgem"
                >
                  <div class="skill-detail-subgem-icon">
                    <img :src="iitem.icon" alt="" />
                  </div>
                  <div class="skill-detail-subgem-tit">{{ iitem.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <NoData v-else text="你可以从以上列表中选取适合你的技能方案"></NoData>
      </div>
      <!-- 宝石浮层，需要计算定位，此处style给的位置仅示意 -->
      <Teleport to="body">
        <div
          v-if="!nodata"
          class="gem-popover"
          :style="`top:3.2rem; left:calc(6.68rem + 2.6rem);`"
        >
          <div class="gem-main">
            <img class="gem-icon" :src="subGemImg" alt="" />
            <div class="gem-info">
              <div class="gem-tit">熔火爆破</div>
              <div class="gem-text">辅助</div>
            </div>
          </div>
          <div class="gem-desc">惊魂宝石限定</div>
          <div class="gem-skill">
            <div class="gem-skill-text">阶级：<span>1</span></div>
            <div class="gem-skill-text">等级：<span>1-3</span></div>
            <div class="gem-skill-text">消耗加成：<span>120%</span></div>
            <div class="gem-skill-text">需求：<span>等级（1-90）</span></div>
            <div class="gem-skill-text">辅助需求：<span>+5 力量</span></div>
          </div>
        </div>
      </Teleport>
    </div>
    <div class="analysis-panel skill-analysis-panel">
      <div class="analysis-hd">
        <div class="analysis-title-wrap">
          <div class="analysis-title"></div>
          <div class="analysis-subtitle"></div>
        </div>
        <div class="analysis-desc">腾讯混元提供支持</div>
      </div>
      <div class="analysis-bd">
        <div class="analysis-markdown bd-analysis-markdown">
          <h3>概况</h3>
          <p>你的BD核心输出不足，群体伤害不足，需优化技能联动和技能AOE能力。</p>
          <h3>章节适配性</h3>
          <p
            >本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。</p
          >
          <h3>调整建议</h3>
          <p>
            本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。本章节冰冻伤害为主，建议您选取含有冰抗的装备，总体冰抗建议≥50%为佳。同时建议采用XXXXX护符。
          </p>
        </div>
      </div>
    </div>
  </PanelController>
</template>
<script lang="ts" setup>
import '@src/assets/scss/pages/skill/index.scss';
import '@src/assets/scss/components/bd-panel.scss';
import '@src/assets/scss/components/analysis-panel.scss';
import '@src/assets/scss/pages/assistant/markdown.scss';
import { ref, computed } from 'vue';
import { useAsyncState } from '@vueuse/core';
import PlannerSelect from '@src/components/planner-select.vue';
import NoData from '@src/components/bd-nodata.vue';
import PanelController from '@src/components/panel-controller.vue';
import { useBd } from '@src/composable/bd';
import authorAvatar from '@src/assets/images/mock/roles2/IconIntFourb_Sorceress1.jpg?url';
import posterImg from '@src/assets/images/mock/skill-poster.jpg?url';
import gemImg from '@src/assets/images/mock/gem.png?url';
import subGemImg from '@src/assets/images/mock/sub-gem.png?url';

const { bdList, fetchBdList } = useBd();
const isSkillShow = ref(false);
// TODO从客户端获取用户职业ID
const currentClassId = ref('1');
const currentAscendancyId = ref('1');
const bdId = ref<string>('');
useAsyncState(
  fetchBdList(currentClassId.value, currentAscendancyId.value),
  null,
  { immediate: true }
);

// 判断是否无数据
const nodata = computed(() => {
  return !bdId.value;
});
const hideMainPanel = () => {
  isSkillShow.value = false;
};
const testProgressList = [
  {
    poster: posterImg,
    name: '冰墙',
    gem: gemImg,
    subGems: [
      {
        icon: subGemImg,
        name: '法术回响'
      },
      {
        icon: subGemImg,
        name: '冰冷专精'
      },
      {
        icon: subGemImg,
        name: '彗星'
      },
      {
        icon: subGemImg,
        name: '能量保留'
      },
      {
        icon: subGemImg,
        name: '动力火车'
      }
    ]
  },
  {
    poster: posterImg,
    name: '冰墙',
    gem: gemImg,
    subGems: [
      {
        icon: subGemImg,
        name: '法术回响'
      },
      {
        icon: subGemImg,
        name: '冰冷专精'
      },
      {
        icon: subGemImg,
        name: '彗星'
      },
      {
        icon: subGemImg,
        name: '能量保留'
      },
      {
        icon: subGemImg,
        name: '动力火车'
      }
    ]
  },
  {
    poster: posterImg,
    name: '冰墙',
    gem: gemImg,
    subGems: [
      {
        icon: subGemImg,
        name: '法术回响'
      },
      {
        icon: subGemImg,
        name: '冰冷专精'
      },
      {
        icon: subGemImg,
        name: '彗星'
      },
      {
        icon: subGemImg,
        name: '能量保留'
      },
      {
        icon: subGemImg,
        name: '动力火车'
      }
    ]
  },
  {
    poster: posterImg,
    name: '冰墙',
    gem: gemImg,
    subGems: [
      {
        icon: subGemImg,
        name: '法术回响'
      },
      {
        icon: subGemImg,
        name: '冰冷专精'
      },
      {
        icon: subGemImg,
        name: '彗星'
      },
      {
        icon: subGemImg,
        name: '能量保留'
      },
      {
        icon: subGemImg,
        name: '动力火车'
      }
    ]
  },
  {
    poster: posterImg,
    name: '暴击时施放',
    gem: gemImg,
    subGems: [
      {
        icon: subGemImg,
        name: '法术回响'
      },
      {
        icon: subGemImg,
        name: '冰冷专精'
      }
    ]
  },
  {
    poster: posterImg,
    name: '暴击时施放',
    gem: gemImg,
    subGems: [
      {
        icon: subGemImg,
        name: '法术回响'
      },
      {
        icon: subGemImg,
        name: '冰冷专精'
      },
      {
        icon: subGemImg,
        name: '彗星'
      },
      {
        icon: subGemImg,
        name: '能量保留'
      }
    ]
  },
  {
    poster: posterImg,
    name: '冰墙',
    gem: gemImg,
    subGems: [
      {
        icon: subGemImg,
        name: '法术回响'
      },
      {
        icon: subGemImg,
        name: '冰冷专精'
      },
      {
        icon: subGemImg,
        name: '彗星'
      }
    ]
  }
];
</script>
