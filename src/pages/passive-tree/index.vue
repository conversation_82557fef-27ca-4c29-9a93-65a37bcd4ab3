<template>
  <PanelController
    v-model:show="isTreeShow"
    panel-type="tree"
    panel-name="passive_skill_panel"
  >
    <PlannerPanel @close="isTreeShow = false" />
    <AiPanel />
  </PanelController>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import '@src/assets/scss/pages/passive-tree/index.scss';
import '@src/assets/scss/components/bd-panel.scss';
import '@src/assets/scss/components/analysis-panel.scss';
import '@src/assets/scss/pages/assistant/markdown.scss';
import PanelController from '@src/components/panel-controller.vue';
import PlannerPanel from './components/planner-panel.vue';
import AiPanel from './components/ai-panel.vue';

const isTreeShow = ref(false);
</script>
