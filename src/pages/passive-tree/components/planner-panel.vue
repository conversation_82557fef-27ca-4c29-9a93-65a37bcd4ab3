<template>
  <div class="bd-panel tree-panel">
    <div class="tree-panel-hd">
      <div class="tree-panel-tit"></div>
      <div class="bd-panel-close" @click="emit('close')"></div>
    </div>
    <div class="tree-panel-bd">
      <PlannerSelect v-model:bd-id="bdId" :list="plannerList" />
      <PlannerHeader v-if="bdId" />
      <PlannerContent :bd-id="bdId" @apply-planner="handleApplyPlanner" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import PlannerSelect from '@src/components/planner-select.vue';
import PlannerHeader from './planner-header.vue';
import PlannerContent from './planner-content.vue';
import { getRecommendBD } from '@src/model/bd-report';
import { useAsyncState } from '@vueuse/core';
import { useBd } from '@src/composable/bd';
import { useUserStats } from '@src/composable/userInfo';
import { callService } from '@src/util/service';
import { reportEvent } from '@src/composable/report';
import { PAGE_NAME } from '@src/model/common';

const emit = defineEmits(['close']);
const {
  fetchSingleBdDetail,
  bdList,
  fetchBdList,
  createNoneOption,
  bdDetails
} = useBd();
const { currentClassId, currentAscendancyId, character, gameVersion } =
  useUserStats();

const bdId = ref<string>('');

// 获取推荐BD并直接赋值
useAsyncState(async () => {
  const result = await getRecommendBD(character.value, gameVersion.value);
  if (result?.bd_id) {
    bdId.value = result.bd_id;
  }
  fetchBdList(currentClassId.value, currentAscendancyId.value);
}, null);

// 包含"无"选项的完整列表
const plannerList = computed(() => {
  return [createNoneOption(currentClassId.value), ...bdList.value];
});

const handleApplyPlanner = () => {
  // 应用 bdList 的首页方案，即推荐方案
  if (plannerList.value?.length > 1) {
    bdId.value = plannerList.value[1].bd_id;
  }
  reportEvent({
    pageName: PAGE_NAME,
    block: 'suggest_bd_apply',
    action: 'click',
    ext: bdId.value
  });
};

// 页面进入上报
onMounted(() => {
  reportEvent({
    pageName: PAGE_NAME,
    block: 'passive_skill_panel',
    action: 'enter'
  });
});

watch(bdId, async newVal => {
  if (newVal) {
    await fetchSingleBdDetail(newVal);
    // 绘制天赋线
    callService(
      'Srv_POE2InGame_SetPassiveSkill',
      bdDetails.value[0]?.passive_skill
    );
  }
});
</script>
