<template>
  <div class="tree-planner-main">
    <div class="tree-main-tit">
      <div
        v-if="bdSingleDetail?.passive_skill_point"
        class="tree-main-tit-item"
      >
        本方案需天赋点数<span>{{ bdSingleDetail?.passive_skill_point }}</span>
      </div>
      <div v-if="userPassiveSkillPoint" class="tree-main-tit-item">
        当前拥有天赋点数<span>{{ userPassiveSkillPoint }}</span>
      </div>
    </div>
    <div class="tree-main-legend">
      <div class="tree-main-legend-item done">已加点</div>
      <div class="tree-main-legend-item recommend">推荐加点</div>
      <div class="tree-main-legend-item weapon-done">武器II已加点</div>
      <div class="tree-main-legend-item weapon-recommend">武器II推荐加点</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStats } from '@src/composable/userInfo';
import { useBd } from '@src/composable/bd';

const { userPassiveSkillPoint } = useUserStats();
const { bdSingleDetail } = useBd();
</script>
