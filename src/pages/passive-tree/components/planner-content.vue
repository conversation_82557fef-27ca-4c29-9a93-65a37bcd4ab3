<template>
  <div class="tree-planner-detail">
    <div v-if="props.bdId" class="tree-planner-detail-inner analysis-markdown">
      <h3>珠宝词条建议</h3>
      <ul v-if="!!bdSingleDetail?.passive_skill_stone?.length">
        <li
          v-for="category in bdSingleDetail.passive_skill_stone"
          :key="category.priority"
        >
          {{ category.label }}<strong></strong>{{ category.items.join(' | ') }}
        </li>
      </ul>
      <h3>基石天赋点</h3>
      <ul>
        <li><u>不变劲风</u>：你拥有秘能涌动</li>
        <li>
          <u>青春永驻</u
          >：能量护盾充能改为生命充能，药剂的生命恢复效果改为套用至能量护盾
        </li>
        <li><u>暴风之心</u>：承受元素伤害的 40% 补偿为能量护盾</li>
      </ul>
      <h3>属性统计</h3>
      <ol>
        <li>基础属性：</li>
        <ul>
          <li>+40 力量</li>
          <li>+10 敏捷</li>
          <li>+130 智慧</li>
        </ul>
        <li>魔力属性：</li>
        <ul>
          <li>+40 力量</li>
        </ul>
        <li>魔力属性：</li>
        <ul>
          <li>+40 力量</li>
        </ul>
        <li>魔力属性：</li>
        <ul>
          <li>+40 力量</li>
        </ul>
        <li>魔力属性：</li>
        <ul>
          <li>+40 力量</li>
        </ul>
        <li>魔力属性：</li>
        <ul>
          <li>+40 力量</li>
        </ul>
      </ol>
    </div>
    <NoData v-else>
      <span>
        你可以从以上列表中选取适合你的天赋方案，<br />
        或通过右键点击天赋节点标出路径，自行模拟方案。
      </span>
    </NoData>
    <NewbieDialog
      v-if="!bdId && Number(userLevel) <= 70 && showNewbieDialog"
      @apply="emits('apply-planner')"
      @cancel="showNewbieDialog = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import NoData from '@src/components/bd-nodata.vue';
import NewbieDialog from './newbie-dialog.vue';
import { useBd } from '@src/composable/bd';
import { useUserStats } from '@src/composable/userInfo';

const props = defineProps<{
  bdId: string;
}>();

const emits = defineEmits(['apply-planner', 'cancel-planner']);

const { userLevel } = useUserStats();
const { bdSingleDetail } = useBd();
const showNewbieDialog = ref(true);
</script>
