import { onBeforeUnmount, ref, shallowRef, getCurrentInstance } from 'vue';
import { fetchEventSource } from '@microsoft/fetch-event-source';

export function useSSE(
  url: string,
  params: Record<string, any>,
  options: {
    onMessage?: (data: {
      content: string;
      reasoning_content: string;
      create_time: number;
      id: string;
      related_questions?: string[];
    }) => void;
    onError?: (e: Error) => void;
    onOpen?: (response: Response) => void;
    onClose?: () => void;
    onDone?: () => void;
  } = {}
) {
  const abortController = new AbortController();
  const { signal } = abortController;
  const reasoningContent = ref('');
  const content = ref('');
  const sessionId = ref('');
  const relatedQuestions = ref<string[]>([]);
  const createTime = ref(0);

  const error = shallowRef<Error | null>(null);
  fetchEventSource(url, {
    method: 'POST',
    body: JSON.stringify(params),
    async onopen(response) {
      if (
        response.ok &&
        response.headers.get('content-type') === 'text/event-stream'
      ) {
        options.onOpen?.(response);
      } else {
        close();
        const newError = new Error('received non-event-stream response');
        options.onError?.(newError);
        error.value = newError;
      }
    },
    onmessage(ev: { data: string }) {
      const { data } = ev;
      if (data === '[DONE]') {
        options.onDone?.();
        close();
        return;
      }
      const {
        content: newContent,
        reasoning_content: newReasoningContent,
        related_questions: newRelatedQuestions,
        session_id: newSessionId,
        create_time: newCreateTime,
        trace_id: newTraceId
      } = JSON.parse(data) as {
        content: string;
        reasoning_content: string;
        session_id: string;
        trace_id: string;
        scene: string;
        related_questions: string[];
        create_time: number;
      };
      createTime.value = newCreateTime * 1000;
      sessionId.value = newSessionId;
      relatedQuestions.value = newRelatedQuestions?.length
        ? newRelatedQuestions
        : relatedQuestions.value;
      content.value += newContent;
      reasoningContent.value += newReasoningContent;
      options.onMessage?.({
        content: content.value,
        reasoning_content: reasoningContent.value,
        create_time: createTime.value,
        id: newTraceId,
        related_questions: relatedQuestions.value
      });
    },
    onerror(error: any) {
      options.onError?.(error);
      console.error('connect to url failed', error);
      close();
    },
    signal
  });

  const close = () => {
    abortController.abort();
  };

  if (getCurrentInstance()) {
    onBeforeUnmount(() => {
      close();
    });
  }

  return {
    content,
    reasoningContent,
    sessionId,
    relatedQuestions,
    createTime,
    error,
    close
  };
}
