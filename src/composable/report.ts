import { sessionReport } from '@src/model/chat';
import { emitter } from '@src/util/event';
import { init, report } from '@tencent/wegame-web-sdk';

type EventOptions = Parameters<ReturnType<typeof init>>[2];

/**
 * 通用的执行上报的方法
 * @param params 上报时携带的参数信息，包括pageName 页面名称、block 模块名等
 * @param eventOptions 额外指定配置
 * @param eventOptions.immediate 实时上报
 * @param eventOptions.sendBeacon 离开页面时上报，在上报后页面会跳转时需指定为true
 * @param eventOptions.realTimeAnalysis 实时上报到TDM
 */
export const reportEvent = (
  params: {
    block: string;
    action: string;
    [x: string]: any;
  },
  eventOptions: EventOptions = {}
) => {
  const {
    immediate = false,
    sendBeacon = false,
    realTimeAnalysis = false
  } = eventOptions;

  const name = 'helper_components';
  report?.reportEvent(name, params, {
    immediate,
    sendBeacon,
    realTimeAnalysis
  });
};

/**
 * 场景化历史记录上报
 * @param historyTitle 历史记录标题
 * @param content 历史记录内容
 */
export function useReport() {
  async function sceneReport(historyTitle: string, content: string) {
    const { sessionId, title } = await sessionReport({
      role: 'operations',
      title: historyTitle,
      content
    });
    emitter.emit('new-session-created', {
      sessionId,
      title
    });
  }

  return {
    sceneReport // 场景化历史记录上报
  };
}
