import { createSharedComposable } from '@vueuse/core';
import { ref } from 'vue';
import { getOssConfig, OssSceneItem } from '@src/model/oss';
import { STAGE, COMMON_STAGES, OSS_TABLE } from '@src/model/common';

export const useShareOss = createSharedComposable(() => {
  const loading = ref(false);

  // 根据 stage 获取对应的 OSS 配置
  async function getOssConfigByStage(stage?: string): Promise<OssSceneItem[]> {
    if (!stage) return [];
    loading.value = true;
    const data = await getOssConfig(OSS_TABLE[stage]);
    loading.value = false;
    return data?.result?.error_code === 0 ? data[OSS_TABLE[stage]] || [] : [];
  }

  // 获取通用型场景化内容
  async function getCommonScenceOssConfig(
    stage?: string
  ): Promise<Partial<OssSceneItem>> {
    const data = await getOssConfigByStage(stage);
    return data?.find(item => item.scence_type === stage) || {};
  }

  // 获取场景化 OSS 数据
  async function getSceneOssData(
    stage: string,
    bossName?: string
  ): Promise<Partial<OssSceneItem>> {
    if (COMMON_STAGES.includes(stage)) {
      return await getCommonScenceOssConfig(stage);
    }
    if (stage === STAGE.BOSS_BATTLE) {
      const bossList = await getOssConfigByStage(stage);
      return bossList.find(item => item.boss_id === bossName) || {};
    }
    return {};
  }

  // 获取角色选择 OSS 数据
  async function getRoleSelectOssData(
    stage: string,
    roleId: number
  ): Promise<OssSceneItem[]> {
    if (stage === STAGE.ROLE_SELECT) {
      const roleList = await getOssConfigByStage(stage);
      return roleList.filter(item => Number(item.role_id) === roleId);
    }
    return [];
  }

  return {
    loading,
    getOssConfigByStage,
    getCommonScenceOssConfig,
    getSceneOssData, // 场景化数据
    getRoleSelectOssData // 角色选择数据
  };
});
