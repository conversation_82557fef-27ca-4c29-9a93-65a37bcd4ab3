import { createSharedComposable } from '@vueuse/core';
import { ref } from 'vue';
import {
  getBdList,
  getBdDetail,
  type BdListInfo,
  type BdDetailInfo
} from '@src/model/bd';

/**
 * BD 方案管理 Composable
 */
export const useBd = createSharedComposable(() => {
  const loading = ref(false);
  const bdList = ref<BdListInfo[]>([]);
  const bdDetails = ref<BdDetailInfo[]>([]);
  const bdSingleDetail = ref<BdDetailInfo | null>(null);

  /**
   * 创建"无"选项，选择则取消应用
   * @param classId 职业ID
   * @returns 无选项对象
   */
  const createNoneOption = (classId: string): BdListInfo => ({
    bd_id: '',
    class_id: classId,
    ascendancy_id: '',
    title: '无',
    author: '',
    tags: []
  });

  /**
   * 获取 BD 方案列表
   * @param classId 职业ID
   * @param ascendancyId 升华职业ID
   */
  const fetchBdList = async (classId: string, ascendancyId: string) => {
    loading.value = true;
    try {
      const result = await getBdList(classId, ascendancyId);
      bdList.value = result?.bd_list || [];
    } catch {
      bdList.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取 BD 方案详情
   * @param bdIdList BD ID列表
   */
  const fetchBdDetail = async (bdIdList: string[]) => {
    loading.value = true;
    try {
      const result = await getBdDetail(bdIdList);
      bdDetails.value = result?.bd_list || [];
    } catch {
      bdDetails.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取单个 BD 方案详情
   * @param bdId BD ID
   */
  const fetchSingleBdDetail = async (bdId: string) => {
    if (!bdId) bdSingleDetail.value = null;
    try {
      await fetchBdDetail([bdId]);
      bdSingleDetail.value = bdDetails.value[0] || null;
    } catch (error) {
      console.error('获取BD方案详情失败:', error);
      bdSingleDetail.value = null;
    }
  };

  // 根据 bd_id 查找当前项
  const getCurrentBdItem = (bdId: string) => {
    return bdList.value.find((item: any) => item.bd_id === bdId) || null;
  };

  return {
    loading,
    bdList,
    bdDetails,
    fetchBdList,
    fetchBdDetail,
    fetchSingleBdDetail,
    createNoneOption,
    bdSingleDetail,
    getCurrentBdItem
  };
});
