import { ref } from 'vue';
import { createSharedComposable } from '@vueuse/core';
import { serviceRequest } from '@src/util/service';
import { log } from '@src/util/log';
import type { UserAllData } from '@src/util/event';

export const useUserStats = createSharedComposable(() => {
  // 用户信息
  const userGameData = ref<UserAllData | null>(null);
  const userLevel = ref('70'); // 用户等级
  const currentClassId = ref('1'); // 用户职业ID
  const currentAscendancyId = ref('1'); // 用户升华职业ID
  const character = ref('polaris'); // 用户角色昵称
  const gameVersion = ref('1.15.0'); // 游戏版
  const userPassiveSkillPoint = ref(90); // 用户拥有的天赋点数

  const isLoading = ref(false);

  // 请求用户统计数据
  const requestUserStats = async () => {
    try {
      isLoading.value = true;
      log('请求用户统计数据: Srv_POE2InGame_ReqAllStats');
      const result = await serviceRequest('Srv_POE2InGame_ReqAllStats');
      userGameData.value = result;
      return result;
    } catch (error) {
      log(`请求用户统计数据失败: ${error}`);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    userGameData,
    isLoading,
    userLevel,
    currentClassId,
    currentAscendancyId,
    character,
    gameVersion,
    userPassiveSkillPoint,
    requestUserStats
  };
});
