import { emitter, BroadcastEventType } from '@src/util/event';
import { service, isWeGame } from '@tencent/wegame-web-sdk';
import { onBeforeUnmount, onMounted } from 'vue';

export function useBroadcast<T extends keyof BroadcastEventType>(
  cmd: T,
  callback: (data: BroadcastEventType[T]) => void
) {
  const handleCallback = (
    data: BroadcastEventType[T] & {
      user_data?: string;
    }
  ) => {
    // 屏蔽服务请求回调的广播
    if (!data.user_data) {
      callback(data);
    }
  };

  onMounted(() => {
    if (!isWeGame) {
      emitter.on(cmd, handleCallback);
    } else {
      service.listen<{
        msg: string;
      }>(cmd, ev => {
        const events: Array<BroadcastEventType[T]> = JSON.parse(ev.msg);
        events.forEach(e => {
          handleCallback(e);
        });
      });
    }
  });

  const clear = () => {
    if (!isWeGame) {
      emitter.off(cmd, handleCallback);
    } else {
      service.unlisten(cmd, handleCallback);
    }
  };

  onBeforeUnmount(() => {
    clear();
  });

  return { clear };
}
