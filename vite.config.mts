/// <reference types="vite-svg-loader" />
/* eslint-disable @tencent/wegame/no-nonstandard-wegame-brand */
import path from 'path';
import { defineConfig, UserConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { createWeGamePlugin } from '@tencent/vite-plugin-wegame';
import svgLoader from 'vite-svg-loader';
import postcssPresetEnv from 'postcss-preset-env';
import autoprefixer from 'autoprefixer';
// import { visualizer } from 'rollup-plugin-visualizer';
import { viteMockServe } from 'vite-plugin-mock';

const baseURL = '/helper/poe2-in-game/';
const projectRoot = path.join(process.cwd(), './');

const env: 'dev' | 'pre' | 'production' = 'pre';

const proxyHost = {
  dev: 'https://************',
  pre: 'https://**************',
  production: 'https://***********'
}[env];

const proxyDomain = {
  dev: 'test.wegame.com.cn',
  pre: 'qa.wegame.com.cn',
  production: 'www.wegame.com.cn'
}[env];

const entries = (process.env.ENTRIES ?? '').split(',').filter(Boolean);

export default defineConfig(() => {
  const plugins: UserConfig['plugins'] = [
    vue(),
    viteMockServe({
      mockPath: 'mock',
      enable: true,
      logger: true
    }),
    createWeGamePlugin({
      root: __dirname,
      entries: entries.length ? entries : ['index.html'],
      pageHost: process.env.WEGAME_PAGE_HOST || proxyDomain,
      baseURL,
      cdnPublicPath: `/g.2002052-r.4de9d${baseURL}`,
      injections: {
        aq: false
      },
      fallback: { target: proxyHost, secure: false },
      ssi: {
        cache: false
      },
      externals: [
        {
          target: 'modern',
          libraries: [
            {
              name: 'vue',
              src: 'https://wegame.gtimg.com/g.55555-r.c4663/lib/vue/latest/dist/vue.runtime.esm-browser.prod.js'
            },
            {
              name: '@tencent/wegame-web-sdk',
              src: 'https://wegame.gtimg.com/g.55555-r.c4663/lib/wegame-web-sdk/1.1.3/dist/index.mjs'
            }
          ]
        }
      ]
    }),
    svgLoader({
      svgoConfig: {
        plugins: [
          {
            name: 'preset-default',
            params: {
              overrides: {
                removeViewBox: false,
                collapseGroups: false
              }
            }
          },
          'removeXMLNS'
        ]
      }
    })
  ];
  const resolveAlias = [
    {
      find: /^assets\//,
      replacement: path.join(projectRoot, '/src/assets/')
    },
    {
      find: /^@src\//,
      replacement: path.join(__dirname, '/src/')
    },
    {
      find: /^shared\//,
      replacement: path.join(__dirname, 'shared/')
    }
  ];

  return {
    root: __dirname,
    plugins,
    resolve: {
      alias: resolveAlias
    },
    css: {
      postcss: {
        plugins: [postcssPresetEnv(), autoprefixer()]
      }
    },
    server: {
      port: 3000,
      host: '0.0.0.0',
      proxy: {
        '^/api': {
          target: proxyHost,
          headers: {
            host: proxyDomain
          }
        },
        '^/fragments': {
          target: proxyHost,
          secure: false,
          headers: {
            host: proxyDomain
          }
        },
        '^/middle': {
          target: proxyHost,
          secure: false,
          headers: {
            host: proxyDomain
          }
        },
        '^/manifest': {
          target: proxyHost,
          secure: false,
          headers: {
            host: proxyDomain
          }
        }
      }
    },
    test: {
      globals: true,
      environment: 'jsdom',
      coverage: {
        enabled: true,
        reporter: ['text']
      }
    }
  };
});
