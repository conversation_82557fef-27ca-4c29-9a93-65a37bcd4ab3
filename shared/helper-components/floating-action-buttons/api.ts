import { baseRequest } from '@tencent/wegame-web-sdk';

/**
 * 获取某游戏下的悬浮按钮配置数据
 */
export async function getFabData(
  appId: string
): Promise<{ qq_group_url?: string; customer_service_url?: string } | null> {
  try {
    const resp = (await baseRequest({
      url: '/api/rail/web/data_filter/game_config/query',
      method: 'POST',
      data: {
        data_names: `helper_components_fab_${appId}`,
        command: 'list_all',
        params: { start_page: 0, items_per_pager: 50, filters: [] }
      }
    })) as {
      items: {
        qrcode?: string;
        qrcode_icon?: string;
        qq_group_url?: string;
        customer_service_url?: string;
      }[];
    };
    if (resp?.items?.[0]) {
      return resp.items[0];
    }
    throw new Error(JSON.stringify(resp));
  } catch (error) {
    console.error(`fetch floating-action-button failed:${error}`);
  }
  return null;
}
