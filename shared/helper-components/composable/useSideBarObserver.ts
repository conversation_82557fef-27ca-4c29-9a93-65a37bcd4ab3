import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { createSharedComposable } from '@vueuse/core';
import { isWeGame } from '@tencent/wegame-web-sdk';

const statusSidebarCSS = {
  0: 'sidebar-hidden', // 单launcher完全收起客户端左侧
  1: 'sidebar-on', // 展开客户端左侧
  2: 'sidebar-off' // 收起客户端左侧
};

export enum SideBarStatus {
  Unknown = 0,
  Expanded = 1, // 侧边栏展开
  Folded = 2 // 侧边栏收起
}

export enum SiderBarStatus {
  Unknown = 0,
  Expanded = 1, // 侧边栏展开
  Folded = 2 // 侧边栏收起
}

type SideBarEvent = {
  cmd: 'switch_side_bar';
  type: SideBarStatus;
};

const status = ref(SideBarStatus.Unknown);

const handleSideBarEvent = (e: MessageEvent) => {
  if (e.data && e.data.cmd !== 'switch_side_bar') {
    return;
  }
  status.value = (e.data as SideBarEvent).type;
};

function sideBarObserver() {
  onMounted(() => {
    if (!isWeGame) {
      status.value = SideBarStatus.Folded;
      return;
    }

    // let frameBodyWidth = 0;

    // const bodyWidth = document.body.offsetWidth;
    // try {
    //   frameBodyWidth = window.top?.document
    //     ? window.top.document.body.offsetWidth
    //     : 0;
    // } catch (err) {}

    // iframe拉通的场景才需要添加额外的class，17是客户端侧边栏与左侧的空隙加上滚动条宽度，若客户端样式修改的话需要修改该数字
    // const diff = 17;
    // if (frameBodyWidth && frameBodyWidth - bodyWidth < diff) {
    status.value = // @ts-ignore
      typeof top !== 'undefined' && top.__CLIENTINFO__
        ? // @ts-ignore
          top!.__CLIENTINFO__!.sideBarStatus
        : SideBarStatus.Expanded;

    window.addEventListener('message', handleSideBarEvent);
    // }

    watch(
      () => status.value,
      val => {
        document.body.classList.remove(
          'sidebar-on',
          'sidebar-off',
          'sidebar-hidden'
        );
        document.body.classList.add(statusSidebarCSS[val]);
      },
      {
        immediate: true
      }
    );
  });

  onBeforeUnmount(() => {
    window.removeEventListener('message', handleSideBarEvent);
  });

  return {
    sideBarStatus: status
  };
}

export const useSideBarObserver = createSharedComposable(sideBarObserver);
