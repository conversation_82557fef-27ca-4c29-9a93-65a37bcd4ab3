import { createSharedComposable } from '@vueuse/core';
import { computed, onMounted, ref } from 'vue';
import { baseRequest } from '@tencent/wegame-web-sdk';

const regionInfoUrl =
  'https://wegame.gtimg.com/g.55555-r.c4663/lib/region/region.json';

export const NATION_WIDE_REGION = {
  code: 1,
  fullName: '全国',
  shortName: '全国'
} as const;

const OVERSEAS_REGION = {
  code: 2,
  fullName: '海外',
  shortName: '海外'
} as const;

const UNKNOWN_REGION = {
  code: 0,
  fullName: '未知',
  shortName: '未知'
} as const;

type RegionInfo = Record<number, { fullName: string; shortName: string }>;

export interface RegionItem {
  code: number;
  fullName: string;
  shortName: string;
}

/** 获取地区json */
async function getRegionInfo() {
  try {
    const resp: RegionInfo = await baseRequest<RegionInfo>({
      url: regionInfoUrl
    });
    return resp;
  } catch (error) {
    console.error('fetch region info failed', error);
  }
  return null;
}

/** 全名转省名 */
export function transFullNameToShort(name: string) {
  if (!name) {
    return '';
  }
  // 中国,广东省,深圳市 => 深圳市
  const area = name.split(',');
  return area[area.length - 1];
}

/** 是否是省份 */
export function isProvince(code: number | string) {
  return /0000$/.test(String(code));
}

/** 城市code获取省份 */
export function getProvince(code: number) {
  const pPrefixCode = String(code).slice(0, 2);
  return Number(`${pPrefixCode}0000`);
}

export const useRegionInfo = createSharedComposable(() => {
  const regionInfo = ref<RegionInfo>({});

  /** 省份信息 */
  const provinceInfo = computed(() => {
    // 按城市分类
    const regionCode = Object.keys(regionInfo.value);
    return regionCode
      .filter(code => {
        return isProvince(code);
      })
      .map(code => {
        return {
          code: Number(code),
          ...regionInfo.value[Number(code)]
        };
      });
  });

  /** 省code获取城市信息 */
  function getCityInfo(pCode: number) {
    if (regionInfo.value[pCode]) {
      const regionCode = Object.keys(regionInfo.value);
      // 区域码是前两位
      const pPrefixCode = String(pCode).slice(0, 2);
      return regionCode
        .filter(code => {
          return String(code).startsWith(pPrefixCode) && !isProvince(code);
        })
        .map(code => {
          return {
            code: Number(code),
            ...regionInfo.value[Number(code)]
          };
        });
    }
    return [];
  }

  // 拉取json
  async function initRegionInfo() {
    const res = await getRegionInfo();
    if (res) {
      regionInfo.value = res;
    }
  }

  /** code获取名字 */
  function regionCodeToName(code: number) {
    if (code === 1) {
      return NATION_WIDE_REGION;
    }
    if (code === 2) {
      return OVERSEAS_REGION;
    }
    if (code === 0) {
      return UNKNOWN_REGION;
    }
    return { ...(regionInfo.value[code] || null), code };
  }

  /** code获取省名 */
  function getProvinceName(code: number) {
    const pCode = getProvince(code);
    return regionCodeToName(pCode) || null;
  }

  onMounted(() => {
    // 省份信息初始化
    initRegionInfo();
  });

  return {
    regionInfo,
    provinceInfo,
    regionCodeToName,
    initRegionInfo,
    getCityInfo,
    isProvince,
    getProvince,
    getProvinceName,
    transFullNameToShort
  };
});
