import { ref, computed, onMounted } from 'vue';
import { createGlobalState, useAsyncState } from '@vueuse/core';
import { cookie } from '@tencent/wegame-web-sdk';
import { btGetUserInfo } from '../api/user-info-api';
import { ACCOUNT_CHANNEL, ACCOUNT_PLATFORM } from '../interface/account';

export const useLoginUser = createGlobalState(() => {
  const { state } = useAsyncState(async () => {
    const tgpId = cookie.get('tgp_id') || '';
    const list = await btGetUserInfo({
      uid: tgpId,
      dst_list: tgpId ? [tgpId] : []
    });
    const item = list.find(item => item.uid === tgpId);
    return item;
  }, null);

  return {
    loginUser: state
  };
});

/**
 * 无效区服ID
 */
export const INVALID_AREA_ID = -1;

export const useAccount = createGlobalState(() => {
  const isLoadedArea = ref(false);
  const curAreaId = ref(INVALID_AREA_ID);

  const areaMapping = ref({});

  const setAreaId = (aid: number) => {
    curAreaId.value = aid;
  };

  const setAreaMapping = (
    mapping: Record<
      number,
      { channel: ACCOUNT_CHANNEL; platform: ACCOUNT_PLATFORM }
    >
  ) => {
    areaMapping.value = mapping;
  };

  const curArea = computed(() => areaMapping.value[curAreaId.value]);

  onMounted(() => {
    curAreaId.value = top?.TGP?.status?.aid || INVALID_AREA_ID;
    isLoadedArea.value = true;
    (window as any).switchZone = (aid: number) => {
      curAreaId.value = aid;
    };
  });

  return {
    areaId: computed(() => curAreaId.value),
    area: curArea,
    isLoadedArea: computed(() => isLoadedArea.value),
    setAreaId,
    setAreaMapping,
    channel: computed<ACCOUNT_CHANNEL>(
      () => curArea.value?.channel ?? ACCOUNT_CHANNEL.UNKNOWN
    ),
    platform: computed<ACCOUNT_PLATFORM>(
      () => curArea.value?.platform ?? ACCOUNT_PLATFORM.UNKNOWN
    )
  };
});
