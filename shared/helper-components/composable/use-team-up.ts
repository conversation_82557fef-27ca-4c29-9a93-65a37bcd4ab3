import { computed, ref, watch } from 'vue';
import { createSharedComposable } from '@vueuse/core';
import jsCookie from 'js-cookie';
import { WebSocketClient } from '../utils/ws';
import {
  getRoomList,
  getTeamUpUserInfo,
  RoomFilter,
  TeamUpRoomInfo,
  TeamUpUser
} from '../team-up/api';

export const useTeamUp = createSharedComposable(() => {
  let ws: WebSocketClient | null = null;
  /**
   * 订阅房间列表间隔，默认10秒
   */
  let subRoomPingGap = 10;
  /**
   * 订阅房间列表超时定时器
   */
  let subRoomTimeoutId: ReturnType<typeof setTimeout> | null = null;
  const curGameId = ref('');
  const curTeamUpAppId = ref('');
  const curAreaId = ref(0);
  const myUserInfo = ref<TeamUpUser | null>(null);
  const curFromSrc = ref('');
  const allRoomList = ref<TeamUpRoomInfo[]>([]);

  /**
   * 需要隐藏的房间ID列表
   */
  const needHideRoomIdList = ref<string[]>([]);
  const tgpId = jsCookie.get('tgp_id');
  const needHideRoomCacheKey = computed(
    () => `helper-team-up-hide-room-${curGameId.value}-${tgpId}`
  );

  const myRoom = computed(() => {
    const room = allRoomList.value.find(
      room =>
        room.room_info.owner === tgpId ||
        room.members.find(member => {
          return member.tgpid === tgpId;
        })
    );
    return room;
  });

  const roomList = computed(() => {
    return allRoomList.value.filter(room => {
      return (
        room.room_info.room_id !== myRoom.value?.room_info.room_id &&
        !needHideRoomIdList.value.includes(room.room_info.room_id)
      );
    });
  });

  /**
   * 当前列表显示的房间ID列表（包含用户自己的房间）
   */
  const activeRoomIdList = computed(() => {
    const myRoomId = myRoom.value?.room_info.room_id || '';
    const list = roomList.value.map(item => item.room_info.room_id);
    return [myRoomId, ...list].filter(Boolean);
  });

  const fetchRoomList = async (filters?: RoomFilter[]) => {
    // 如果没有传过滤条件则用默认数据
    const finalFilters =
      filters && filters.length > 0
        ? filters
        : myUserInfo.value?.preference?.get_room || [];
    const roomObj = await getRoomList({
      teamUpAppId: curTeamUpAppId.value,
      areaId: curAreaId.value,
      filters: finalFilters
    });
    allRoomList.value = roomObj.rooms;
  };

  const initHiddenRooms = () => {
    try {
      const idListStr = sessionStorage.getItem(needHideRoomCacheKey.value);
      needHideRoomIdList.value = idListStr ? JSON.parse(idListStr) : [];
    } catch (error) {
      needHideRoomIdList.value = [];
    }
  };

  const subRoom = () => {
    ws?.subRoom([...activeRoomIdList.value, `${curAreaId.value}`]);
    startTimer();
  };

  const startTimer = () => {
    clearTimer();
    subRoomTimeoutId = setTimeout(() => {
      subRoom();
    }, subRoomPingGap * 1000);
  };

  const clearTimer = () => {
    if (subRoomTimeoutId) {
      clearTimeout(subRoomTimeoutId);
    }
  };

  const handleWebSocketMessage = (message: string) => {
    if (message === 'ok') return;
  };

  const init = async ({
    gameId,
    teamUpAppId,
    areaId,
    fromSrc
  }: {
    gameId: string;
    teamUpAppId: string;
    areaId?: number;
    fromSrc?: string;
  }) => {
    curGameId.value = gameId;
    curTeamUpAppId.value = teamUpAppId;
    curAreaId.value = areaId || 0;
    curFromSrc.value = fromSrc || 'team_up';
    myUserInfo.value = await getTeamUpUserInfo({
      teamUpAppId,
      areaId,
      fromSrc
    });
    initHiddenRooms();
    fetchRoomList();
    ws = new WebSocketClient(
      `wss://${location.host}/api/ws/wegame.platform.game.WsConn?appid=${teamUpAppId}`,
      handleWebSocketMessage
    );
    subRoom();
  };

  watch(
    () => activeRoomIdList.value,
    val => {
      // 超过一百个应该程序有异常，没触发刷新房间列表（因为现在前端不解散房间【除了我的房间】）
      if (val.length > 100) {
        fetchRoomList();
      } else {
        subRoom();
      }
    }
  );

  return {
    init
  };
});
