import { ref, toRef, UnwrapRef, watchEffect, type Ref } from 'vue';

const isBrowser = typeof window !== 'undefined';

export type UseAsyncOptions = {
  initLoading?: boolean;
  enabled?: boolean | Ref<boolean>;
};
export function useAsync<T>(
  func: () => Promise<T>,
  options: UseAsyncOptions = {}
) {
  const { initLoading = false, enabled = true } = options;
  const hookEnabled = toRef(enabled);
  const loading = ref(initLoading);
  const data = ref<T | null>(null);
  const error = ref<Error | null>(null);
  let rejectHandler: ((e: Error) => void) | null;
  const fetchData = () =>
    new Promise<T>((resolve, reject) => {
      rejectHandler = reject;
      resolve(func());
    });

  const refetch = async () => {
    rejectHandler?.(new Error('abort'));
    rejectHandler = null;
    loading.value = true;
    await fetchData()
      .then(res => {
        data.value = res as UnwrapRef<T>;
        error.value = null;
        loading.value = false;
      })
      .catch((e: Error) => {
        if (e.message !== 'abort') {
          error.value = e;
          loading.value = false;
        }
      });
  };
  isBrowser &&
    watchEffect(() => {
      if (hookEnabled.value) {
        refetch();
      }
    });
  return { loading, data, error, refetch };
}
