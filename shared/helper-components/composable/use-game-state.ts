import { computed, isRef, onMounted, ref, type Ref } from 'vue';
import { createGlobalState } from '@vueuse/core';
import { service } from '@tencent/wegame-web-sdk';
import { isV3 } from '../utils/wg';

export enum GameState {
  Unknown = 0 /* 未知状态 */,
  Normal = 1 /* 普通状态(可运行) */,
  Running = 2 /* 运行中 */,
  Broken = 3 /* 版本不支持 */,
  NeedUpdate = 4 /* 需要更新 */,
  Updating = 5 /* 正在更新 */,
  UpdateFail = 6 /* 更新失败 */,
  ServerUnAvailable = 7 /* 服务器维护 */,
  NotInstalled = 8 /* 未安装 */,
  Downloading = 9 /* 下载中 */,
  DownloadPaused = 10 /* 下载暂停 */,
  Downloaded = 11 /* 已下载(未安装) */,
  DownloadFail = 12 /* 下载失败 */,
  Installing = 13 /* 正在安装 */,
  Uninstalling = 14 /* 正在卸载 */,
  SelectingServer = 15 /* 正在选区 */,
  Launching = 16 /* 正在启动 */,
  Repairing = 17 /* 正在修复 */,
  RepairFail = 18 /* 修复失败 */,
  UpdatePaused = 19 /* 更新暂停 */,
  Unavailable = 20, // 不可用
  Packing = 21 // 深度优化
}

export const useGameState = createGlobalState(
  (gameId: Ref<string> | string) => {
    const curGameId = isRef(gameId) ? gameId : ref(gameId);
    const gameState = ref(GameState.Unknown);

    /**
     * 是否运行中(只判断游戏运行，不跟gameFlow挂钩)
     */
    const isRunning = computed(() => {
      return gameState.value === GameState.Running;
    });

    const initEvents = () => {
      if (isV3()) {
        service.listen<{ game_id: string; game_state: { state: GameState } }>(
          'Msg_GameLibrary_UpdateGameState',
          data => {
            if (String(data.game_id) === curGameId.value) {
              gameState.value = data.game_state.state;
            }
          }
        );
      } else {
        service.listen_broadcast(
          'game_library_broadcast_update_game_state',
          (data: { game_id: number; state: GameState }) => {
            if (String(data.game_id) === curGameId.value) {
              gameState.value = data.state;
            }
          }
        );
      }
    };

    const fetchData = () => {
      if (isV3()) {
        service
          .call<{
            game_state: { state: GameState };
          }>('Srv_GameLibrary_GetGameState', { game_id: curGameId.value })
          .then(resp => {
            gameState.value = resp.game_state.state;
          });
      } else {
        service
          .call<{ state: GameState }>('Svr_GetGameState', {
            game_id: curGameId.value
          })
          .then(resp => {
            gameState.value = resp.state;
          });
      }
    };

    onMounted(() => {
      fetchData();
      initEvents();
    });

    return {
      gameState,
      /**
       * 是否游戏运行中
       */
      isRunning
    };
  }
);
