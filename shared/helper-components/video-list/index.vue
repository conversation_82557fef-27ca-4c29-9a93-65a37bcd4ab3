<template>
  <div
    v-if="tabList.length"
    ref="container"
    :data-appid="appId"
    data-block="videoList"
    class="helper-panel helper-panel--video"
  >
    <div class="helper-panel-main">
      <!-- 面板容器 头部 S -->
      <div class="helper-panel-header">
        <div class="helper-panel-title">
          <strong class="helper-panel-title-text">{{ title }}</strong>
        </div>
        <!-- 面板容器 工具栏 S -->
        <div class="helper-panel-tools">
          <ul v-if="hasTabs" class="panel-tools-tab">
            <li
              v-for="(item, index) in tabList"
              :key="index"
              class="panel-tools-tab-menu"
              :class="{ current: index === curTabIndex }"
              @click="clickTabIndex(index)"
            >
              <a href="javascript:;" class="panel-tools-tab-item">
                {{ item.title }}
              </a>
            </li>
          </ul>
        </div>
        <!-- 面板容器 工具栏 E -->
      </div>
      <!-- 面板容器 头部 E -->
      <div class="helper-panel-cont">
        <!-- 列表 S -->
        <div :class="['video-list', listClass]">
          <ul class="video-list-inner">
            <!-- 视频列表项 S  -->
            <VideoItem
              v-for="(item, index) in list"
              :key="item.vid"
              :item="item"
              @click="playVideoClicked(index)"
            />
            <!-- 视频列表项 E  -->
          </ul>
        </div>
        <!-- 列表 E -->
      </div>
    </div>
    <!-- 面板背景 S  -->
    <div class="helper-panel-bg"></div>
    <!-- 面板背景 E  -->
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { ISuperPlayerConfig } from '@tencent/super-player';

import { show as showVideoDialog } from '../video-dialog';
import VideoItem from './video-item.vue';
import { VideoItem as IVideoItem } from './interface';
import { getVideoList } from './api';
import { reportEvent, reportExposure } from '../utils/report';

const emits = defineEmits<{
  (e: 'show-dialog', data: { index: number; tabIndex: number }): void;
  (e: 'change-tab-index', data: { tabIndex: number }): void;
  (e: 'close-dialog'): void;
}>();

const props = withDefaults(
  defineProps<{
    appId: string;
    title?: string;
    rows?: number; // x行
    cols?: number; // x列
    videoDialogOpt?: {
      slideWidth?: number;
      autoPlayNextVideo?: boolean;
    };
    videoPlayerOpt?: {
      customClass?: string | string[];
      offScreenPause?: boolean;
      config?: Omit<ISuperPlayerConfig, 'container'>;
      volume?: number;
    };
    sortFunc?: (a: IVideoItem, b: IVideoItem) => number;
    checkNewFunc?: (item: IVideoItem) => boolean;
    groupFunc?: (list: IVideoItem[]) => IVideoItem[];
  }>(),
  {
    title: '视频攻略',
    rows: 2,
    cols: 3,
    videoDialogOpt: () => {
      return { slideWidth: 680 };
    },
    videoPlayerOpt: () => {
      return {};
    }
  }
);

const container = ref(null);
const activeIndex = ref(0);
const isShowVideoPreview = ref(false);

const tabList = ref<
  { key: string; index: number; title: string; list: IVideoItem[] }[]
>([]);

const hasTabs = computed(() => {
  return tabList.value.filter(t => t.title).length > 1;
});

const listClass = computed(() => {
  if (props.cols) {
    return `video-list--cols-${props.cols}`;
  }
  return 'video-list--cols-3';
});

const playVideoClicked = (index: number) => {
  activeIndex.value = index;
  isShowVideoPreview.value = true;
  emits('show-dialog', { index, tabIndex: curTabIndex.value });
  const opt: any = {
    appId: props.appId,
    list: list.value,
    activeIndex: activeIndex.value,
    ...props?.videoDialogOpt,
    videoPlayerOpt: { ...props?.videoPlayerOpt }
  };
  reportEvent({
    appId: props.appId,
    block: 'video_list',
    action: 'show_dialog',
    ext: index
  });
  showVideoDialog(opt).catch(() => {
    emits('close-dialog');
  });
};

const fetchData = async () => {
  const pageSize = props.cols * props.rows;
  tabList.value = await getVideoList({
    appId: props.appId,
    pageSize,
    sortFunc: props.sortFunc,
    checkNewFunc: props.checkNewFunc,
    groupFunc: props.groupFunc
  });
};

const curTabIndex = ref(0);

const list = computed(() => {
  return tabList.value?.[curTabIndex.value]?.list || [];
});

const clickTabIndex = (index: number) => {
  curTabIndex.value = index;
  emits('change-tab-index', { tabIndex: index });
  reportEvent({
    appId: props.appId,
    block: 'video_list',
    action: 'click_tab_index',
    ext: index
  });
};

onMounted(() => {
  fetchData();
  if (container.value) {
    reportExposure([container.value]);
  }
});
</script>

<style lang="scss">
@use './assets/scss/video-list.scss';
</style>
