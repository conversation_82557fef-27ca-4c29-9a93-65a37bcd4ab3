import dayjs from 'dayjs';

import { baseRequest } from '@tencent/wegame-web-sdk';

import { zipArrays } from '../utils/data';
import type { VideoItem } from './interface';

/**
 * 获取某游戏下的视频列表
 */
export async function getVideoList({
  appId,
  pageSize = 6,
  sortFunc,
  checkNewFunc,
  groupFunc
}: {
  appId: string;
  pageSize?: number;
  sortFunc?: (a: VideoItem, b: VideoItem) => number;
  checkNewFunc?: (item: VideoItem) => boolean;
  groupFunc?: (list: VideoItem[]) => VideoItem[];
}): Promise<
  { key: string; index: number; title: string; list: VideoItem[] }[]
> {
  try {
    // 最多显示x个
    const PAGE_SIZE = pageSize;
    const resp = (await baseRequest<{
      items: any[];
    }>({
      url: '/api/rail/web/data_filter/game_config/query',
      method: 'POST',
      data: {
        data_names: `helper_components_video_list_${appId}`,
        command: 'list_all',
        params: { start_page: 0, items_per_pager: 50, filters: [] }
      }
    })) as { items?: VideoItem[] };

    if (resp?.items) {
      const tabStrList: string[] = [];
      const list = resp.items.map(item => {
        if (!tabStrList.includes(item.tab)) {
          tabStrList.push(item.tab);
        }
        return {
          ...item,
          publishTime: dayjs(item.publish_time),
          isNew: checkNewFunc ? checkNewFunc(item) : !!Number(item.is_new),
          order: Number(item.order)
        };
      });
      if (sortFunc) {
        list.sort(sortFunc);
      } else {
        list.sort((a: VideoItem, b: VideoItem) => a.order - b.order);
      }
      const tabList = tabStrList
        .map(str => {
          const strList = str.split('-');
          return {
            key: str,
            index: Number(strList[0]),
            title: strList[1],
            list: []
          };
        })
        .sort((a, b) => a.index - b.index);

      list.forEach(item => {
        const tempTabList = tabList.find(itemTab => itemTab.key === item.tab);
        if (tempTabList) {
          (tempTabList.list as VideoItem[]).push(item);
        }
      });
      // 全部视频，每个分类取第一个，数据不到就继续取
      const allVideoList: VideoItem[] = groupFunc
        ? groupFunc(list).slice(0, PAGE_SIZE)
        : zipArrays(
            PAGE_SIZE,
            Object.values(tabList).map(item => item.list)
          );
      // 全部视频列表都限制个数
      for (const item of tabList) {
        item.list = item.list.slice(0, PAGE_SIZE);
      }

      return [
        { key: 'all', index: -99, title: '全部', list: allVideoList },
        ...tabList
      ];
    }
    throw new Error(JSON.stringify(resp));
  } catch (error) {
    console.error(`fetch video list failed:${error}`);
  }
  return [];
}
