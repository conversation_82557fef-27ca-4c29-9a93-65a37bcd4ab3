.helper-video-bg {
  width: 100%;
  height: 100%;
  position: relative;
  pointer-events: none;
  --mask-color: rgba(0, 0, 0, 0.6);
}
.helper-video-ele {
  position: absolute;
  inset: 0;
  z-index: 10;
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: relative;
    z-index: 10;
  }
}
.helper-image.helper-video-img {
  position: absolute;
  inset: 0;
  z-index: 8;
  img {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }
}
.helper-image.helper-video-blur-img {
  position: absolute;
  inset: 0;
  z-index: 5;
  img {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }
}

.fill-contain {
  .helper-video-ele {
    aspect-ratio: 16 / 9;
    bottom: auto;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    -webkit-mask-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0) 5%,
      rgb(0, 0, 0) 30%,
      rgb(0, 0, 0) 70%,
      rgba(0, 0, 0, 0) 100%
    );
  }
}

.helper-video-mask {
  position: absolute;
  inset: 0;
  z-index: 20;
  overflow: hidden;
}

.helper-video-mask-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--helper-navigation-height, 54px) + 80px);
  background: linear-gradient(to bottom, var(--mask-color) 0, transparent 100%);
}
.helper-video-mask-left {
  width: 6rem;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background: linear-gradient(
    to right,
    var(--mask-color) 0,
    var(--mask-color) 182px,
    transparent 100%
  );
  transition: transform 0.3s ease-in-out;
}
.helper-video-mask-right {
  width: 3rem;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to left, var(--mask-color) 0, transparent 100%);
}
.helper-video-mask-bottom {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: calc(var(--helper-launcher-height, 54px) + 80px);
  background: linear-gradient(to top, var(--mask-color) 0, transparent 100%);
}
.show-blur-bg {
  .helper-video-mask {
    background: rgba($color: #000000, $alpha: 0.12);
  }
  .helper-video-ele {
    opacity: 0;
  }
}
// 端内
body:not(.page-out-client) {
  &.sidebar-off,
  &.sidebar-hidden {
    .helper-video-mask-left {
      transform: translate3d(-3rem, 0, 0);
    }
  }
  &.sidebar-on {
    .helper-video-mask-left {
      transform: translate3d(0, 0, 0);
    }
  }
}
// 端外
.page-out-client {
  .helper-video-mask-top,
  .helper-video-mask-bottom {
    opacity: 0;
  }
  .helper-video-mask-left {
    transform: translate3d(-3rem, 0, 0);
  }
}
