<template>
  <!-- 禁用态: helper-radiobox helper-radiobox--disabled  -->
  <div
    :class="['helper-radiobox', { 'helper-radiobox--disabled': disabled }]"
    @click.stop="clickChange"
  >
    <div class="helper-radiobox-ele">
      <!-- input禁用态: 增加属性 disabled="true"  -->
      <!-- input选中态: 增加属性 checked="checked"  -->
      <input
        :id="id"
        type="radio"
        class="helper-radiobox-input"
        :name="name"
        :checked="modelValue"
        :disabled="disabled"
        @change.stop="handleChange"
      />
      <span class="helper-radiobox-icon"> </span>
    </div>
    <label class="helper-radiobox-txt" :for="id">{{ label }} </label>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    label: string;
    name: string;
    modelValue?: boolean;
    id?: string;
    disabled?: boolean;
  }>(),
  {
    id: '',
    disabled: false
  }
);

const emits = defineEmits<(e: 'update:modelValue', checked: boolean) => void>();

const handleChange = () => {
  if (props.disabled) {
    return;
  }
  emits('update:modelValue', !props.modelValue);
};

const clickChange = () => {
  if (props.disabled) {
    return;
  }
  if (props.modelValue) {
    return;
  }

  emits('update:modelValue', !props.modelValue);
};
</script>

<style lang="scss">
@use './assets/scss/helper-radio.scss';
</style>
