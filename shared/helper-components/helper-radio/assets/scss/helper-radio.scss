.helper-radiobox {
    cursor: pointer;
    &:hover {
      .helper-radiobox-txt {
        opacity: 1;
      }
      .helper-radiobox-icon {
        opacity: 1;
      }
    }
    --ui-transition-opacity-1: opacity 0.25s ease;
    --ui-helper-radiobox-border: #3D596D;
  }
  .helper-radiobox-ele {
    width: 12px;
    height: 12px;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    margin: 0 4px 0 0;
  }
  .helper-radiobox-icon {
    width: 12px;
    height: 12px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    border-radius: 50%;
    border: 1px solid var(--ui-helper-radiobox-border);
    opacity: 0.4;
    transition: var(--ui-transition-opacity-1);
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate3d(-50%,-50%,0);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: var(--ui-helper-radiobox-border);
      opacity: 0;
      transition: opacity 0.25s ease;
    }
  }
  .helper-radiobox-input {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    width: 12px;
    height: 12px;
    cursor: pointer;
    &:checked + .helper-radiobox-icon {
      &::after {
        opacity: 1;
      }
      opacity: 1;
    }
  }
  
  .helper-radiobox-txt {
    display: inline-block;
    vertical-align: middle;
    line-height: 12px;
    font-size: 12px;
    color: var(--color-black);
    opacity: 0.8;
    cursor: pointer;
    transition: var(--ui-transition-opacity-1);
  }
  
  .helper-radiobox--disabled {
    cursor: default;
    .helper-radiobox-input {
      cursor: default;
      pointer-events: none;
      &:checked + .helper-radiobox-icon {
        opacity: 0.4;
      }
    }
    .helper-radiobox-icon {
      cursor: default;
      pointer-events: none;
      filter: grayscale(1);
      opacity: 0.4;
    }
    .helper-radiobox-txt {
      opacity: 0.6;
      pointer-events: none;
    }
    &:hover {
      .helper-radiobox-icon {
        opacity: 0.4;
      }
      .helper-radiobox-txt {
        opacity: 0.6;
      }
    }
  }
  
  .ui-dialog-alert .ui-dialog-alert-tit {
    .helper-radiobox-txt {
      opacity: 1;
    }
  }
  .ui-dialog-alert .ui-dialog-alert-desc {
    .ui-checkbox-txt {
      opacity: 1;
    }
  }
  