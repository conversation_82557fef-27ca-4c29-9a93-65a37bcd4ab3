import { baseRequest } from '@tencent/wegame-web-sdk';

let dataLoadPromise: ReturnType<typeof loadFeedsManifest> | null = null;

export const loadFeedsManifest = async (): Promise<{
  'feeds-sdk'?: {
    default: {
      script: string;
      style: string;
    };
  };
}> => {
  if (dataLoadPromise) {
    return dataLoadPromise;
  }

  try {
    dataLoadPromise = baseRequest<{
      'feeds-sdk': {
        default: {
          script: string;
          style: string;
        };
      };
    }>({
      url: '/manifest/index.json'
    });
    const resp = await dataLoadPromise;
    return resp;
  } catch (error) {
    console.error('fetch manifest json failed: ', error);
  }

  return {};
};
