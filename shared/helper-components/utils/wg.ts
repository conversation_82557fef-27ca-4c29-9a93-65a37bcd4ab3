import { cookie } from '@tencent/wegame-web-sdk';

/**
 *
 * @returns 是否是WeGame3.0版本
 */
export const isV3 = () => {
  const result = navigator.userAgent.match(/WeGame\/([^ ]+)/);
  if (result?.[1]) {
    const version = result[1].split('.')?.[0];
    return Number(version) >= 6;
  }
  return false;
};

export const adaptiveWeGameVersionUI = () => {
  if (isV3()) {
    document.body.classList.add('helper-v3');
  }
};

/**
 *
 * @returns 是否是网吧版
 */
export const checkIsNetBar = () => {
  try {
    return navigator.userAgent.indexOf('ChannelId/1') > -1;
  } catch {}
  return false;
};

/**
 * WeGame用户类型
 */
export const WG_USER_TYPE = {
  QQ: '0',
  WX: '1'
};

/**
 * 获取WeGame用户类型
 */
export const getWgUserType = () => {
  if (cookie.get('tgp_user_type') === '1') {
    return WG_USER_TYPE.WX;
  }
  return WG_USER_TYPE.QQ;
};
