/**
 * 向给定 URL 添加或修改参数。
 *
 * @param url 输入的 URL 字符串
 * @param params 一个包含要添加或修改的参数的键值对对象
 * @returns 修改后的 URL 字符串
 */
export const setUrlParameter = (
  url: string,
  params: Record<string, string>
): string => {
  try {
    const urlObj = new URL(url);
    Object.entries(params).forEach(([key, val]) => {
      urlObj.searchParams.set(key, val);
    });
    return urlObj.toString();
  } catch (error) {
    console.error(error);
  }
  return url;
};
