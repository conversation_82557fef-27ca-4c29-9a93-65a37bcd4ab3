import { loadjs } from '@tencent/wegame-web-sdk';

let sdkLoadPromise: undefined | Promise<any> = undefined;

const getCDNHost = () => {
  const envPrefix = location?.hostname.split('.')[0];
  return `${envPrefix && envPrefix !== 'www' ? `${envPrefix}.` : ''}wegame.gtimg.com`;
};

const loadMarketingFloaterSdk = (sdkURL?: string) => {
  if (sdkLoadPromise) {
    return sdkLoadPromise;
  }
  const url =
    sdkURL ||
    `//${getCDNHost()}/g.55555-r.c4663/lib/marketing-floater/latest/marketing-floater.js`;
  sdkLoadPromise = loadjs([url], {
    returnPromise: true
  });

  sdkLoadPromise?.catch(error => {
    console.error('load marketing-floater sdk failed:', error);
  });

  return sdkLoadPromise;
};

export const initMarketingFloater = async (gameId: string, sdkURL?: string) => {
  await loadMarketingFloaterSdk(sdkURL);
  window.MarketingFloater?.load(gameId);
};

declare global {
  interface Window {
    MarketingFloater: {
      load: (gameId: string) => void;
    };
  }
}
