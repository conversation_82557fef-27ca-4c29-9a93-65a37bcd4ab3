/**
 * 获取每个数组的第一个（逐次递增）拼凑成新的数据
 * @param limit
 * @param arrays
 * @returns
 */
export const zipArrays = <T>(limit: number, arrays: T[][]): T[] => {
  const result: T[] = [];
  let index = 0;

  while (result.length < limit) {
    let added = false;

    for (const arr of arrays) {
      if (index < arr.length) {
        result.push(arr[index]);
        if (result.length === limit) {
          break;
        }
        added = true;
      }
    }

    if (!added) {
      break;
    }

    index += 1;
  }

  return result;
};
