import jsCookie from 'js-cookie';
import { baseRequest } from '@tencent/wegame-web-sdk';

/**
 * 获取QQ群URL
 * @param gameId
 * @returns
 */
export async function getQQGroup(
  gameId: string
): Promise<{ joinLink?: string; errorCode?: number }> {
  const userId = jsCookie.get('tgp_id') || '';
  const userType = jsCookie.get('tgp_user_type') || ''; // 微信用户值为1
  // 未登录或者微信用户不返回数据
  if (!userId || userType === '1') {
    return {};
  }
  try {
    const res = (await baseRequest({
      method: 'POST',
      url: '/api/v1/wegame.rail.game.GameOfficalGroup/GetLink',
      data: {
        game_id: gameId
      }
    })) as {
      result: {
        error_code: number;
      };
      group_info_list: { link: string }[];
    };
    const errorCode = res.result?.error_code;
    const joinLink = res.group_info_list?.[0]?.link;
    if (errorCode === 0 && joinLink) {
      return { joinLink };
    }
    throw { errorCode };
  } catch (err) {
    console.error(`getQQGroup of ${gameId} error: `, err);
    return (err || {}) as { joinLink?: string; errorCode?: number };
  }
}
