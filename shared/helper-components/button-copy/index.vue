<template>
  <div
    class="button-copy"
    @click="clickCopy"
    @mouseenter="handleMouseenterCopyBtn"
    @mouseleave="handleMouseleaveCopyBtn"
  >
    <span class="button-copy-icon"></span>
    <span v-if="buttonText" class="button-copy-text">{{ buttonText }}</span>
    <div :class="['button-copy-tips', { hide: copySucceed }]">
      {{ !copySucceed ? beforeText : afterText }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import './assets/scss/index.scss';
import { ref } from 'vue';
import { isWeGame } from '@tencent/wegame-web-sdk';

const props = withDefaults(
  defineProps<{
    copyText: string;
    buttonText?: string;
    beforeText?: string;
    afterText?: string;
  }>(),
  {
    beforeText: '点击复制',
    afterText: '复制成功'
  }
);

const copySucceed = ref<boolean>(false);
const isShowCopyTips = ref<boolean>(false);

const clickCopy = async () => {
  try {
    const text = props.copyText;
    if (isWeGame) {
      // WeGame客户端无法使用navigator.clipboard.writeText
      window.external.callcpp(
        'set_clipboard',
        JSON.stringify({ data: { msg: text, type: 0 } }) // type 0为文字， 1为图片，图片传buffer
      );
    } else {
      await navigator.clipboard.writeText(text);
    }
    copySucceed.value = true;
  } catch (err) {
    console.error(err);
    copySucceed.value = false;
  }
};

const handleMouseenterCopyBtn = () => {
  isShowCopyTips.value = true;
};

const handleMouseleaveCopyBtn = () => {
  isShowCopyTips.value = false;
  copySucceed.value = false;
};
</script>
