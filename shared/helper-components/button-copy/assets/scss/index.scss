@charset "utf-8";
.button-copy{
  //复制按钮图标及文字色
  --button-copy-color-text:rgba(0,0,0,0.5);
  --button-copy-color-text-hover:#000;
  //复制提示文字色
  --button-copy-tips-text:#FFF;
  //复制提示层背景色
  --button-copy-tips-fill:rgba(0,0,0,0.8);
  //复制按钮字号
  --button-copy-text-size:clamp(12px,0.12rem,0.12rem);

  min-width: 0.2rem;
  min-height: 0.4rem;
  display:inline-flex;
  align-items:center;
  justify-content: center;
  position: relative;
  color: var(--button-copy-color-text);
  font-size: var(--button-copy-text-size);
  &:hover{
    color: var(--button-copy-color-text-hover);
    .button-copy-tips{
      opacity: 1;
      transform: translate3d(-50%,calc(-50% - 0.25rem),0);
    }
  }
}

.button-copy-icon{
  width: 0.2rem;
  height: 0.2rem;
  display: inline-block;
  -webkit-mask: url("./../images/icon-copy.png") no-repeat 50% 50%;
  -webkit-mask-size: 100% auto;
  background: var(--button-copy-color-text);
}
.button-copy-tips{
  position:absolute;
  top:50%;
  left:50%;
  transform: translate3d(-50%,calc(-50% - 0.2rem),0);
  color: var(--button-copy-tips-text);
  background: var(--button-copy-tips-fill);
  font-size: var(--button-copy-text-size);
  padding: 0.06rem 0.1rem;
  opacity: 0;
  transition: opacity 0.15s linear,transform 0.15s ease-in-out;
  pointer-events: none;
  &.hide{
    animation: fadeOutCopyTips 0.2s linear 0.5s 1 both;
  }
}
@keyframes fadeOutCopyTips {
  0%{
    opacity: 1;
  }
  100%{
    opacity: 0;
  }
}