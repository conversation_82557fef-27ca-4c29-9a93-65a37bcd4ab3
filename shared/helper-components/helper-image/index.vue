<template>
  <div
    ref="el"
    class="helper-image"
    :class="{ 'helper-image--error': isError }"
  >
    <Transition name="image-transition">
      <img
        v-if="!isError && src"
        :alt="``"
        class="helper-image-pic"
        :src="src"
        :loading="lazy ? 'lazy' : 'eager'"
        @load="onLoaded"
        @error="onError"
      />
    </Transition>
    <slot></slot>
    <span
      v-if="loadingVisible && !isLoaded"
      size="small"
      class="helper-image-loading"
    >
      加载中…
    </span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{ src?: string; lazy?: boolean; loadingVisible?: boolean }>(),
  {
    src: '',
    lazy: false,
    loadingVisible: false
  }
);

const isError = ref(false);
// 视频节点
const el = ref<HTMLElement>();
const isLoaded = ref(false);

const onLoaded = () => {
  isError.value = false;
  isLoaded.value = true;
};
const onError = () => {
  isError.value = true;
};

// src变化后，重置状态
watch(
  () => props.src,
  () => {
    isLoaded.value = false;
    isError.value = false;
  }
);
</script>

<style lang="scss">
@use './index.scss';
</style>
