import { baseRequest } from '@tencent/wegame-web-sdk';

const fetchClusterId = async (gameId: string) => {
  return baseRequest<{ cluster_id: string }>({
    url: '/api/v1/wegame.rail.game.Hybird/GetWebHookClusterID',
    data: { game_id: Number(gameId) },
    method: 'POST'
  });
};

const fetchRecallItems = async (gameId: string) => {
  return baseRequest<{ items: { type: string; title: string; url: string }[] }>(
    {
      url: '/api/rail/web/data_filter/game_config/query',
      method: 'POST',
      data: {
        data_names: `helper_recall_type_${gameId}`,
        command: 'list_all',
        params: { start_page: 0, items_per_pager: 50, filters: [] }
      }
    }
  );
};

export const getRecall = async (gameId: string) => {
  try {
    const [clusterResponse, itemsResponse] = await Promise.all([
      fetchClusterId(gameId),
      fetchRecallItems(gameId)
    ]);
    const clusterId = clusterResponse?.cluster_id?.toString();
    if (clusterId) {
      const recallItem = itemsResponse?.items?.find(
        item => item.type === clusterId
      );
      return recallItem;
    }
  } catch (error) {
    console.error('Failed to fetch recall data:', error);
  }
  return null;
};
