import { SlideItem } from '../carousel/interface';

/**
 * 获取轮播图列表
 * @param appId 游戏ID
 * @returns 轮播图数据列表
 */
export const getBannerList = async (appId: string) => {
  const resp = await fetch(
    `/api/pallas/static/template_data/${appId}/banner.json`
  );
  const bannerJson: { tabs: { items: string[][] }[] } = resp.ok
    ? await resp.json()
    : null;
  if (bannerJson) {
    const items = bannerJson.tabs?.[0].items || [];
    if (items.length > 1) {
      const [prop1, prop2, prop3, prop4, prop5] = items[0];

      const list: SlideItem[] = items
        .slice(1)
        .reduce<SlideItem[]>((acc, cur) => {
          const newItem = {
            [prop1]: cur[0],
            [prop2]: cur[1],
            [prop3]: cur[2],
            [prop4]: cur[3],
            [prop5]: cur[4]
          };

          acc.push({
            title: newItem.title,
            pic: newItem.pic_url,
            jump_url: newItem.jump_url,
            jump_url_type: 'new_wegame_window'
          });

          return acc;
        }, []);
      return list;
    }
  }
  return [];
};

export interface InfoItem {
  title: string;
  release_time: string;
  jump_url: string;
  abstract: string;
}

/**
 * 获取资讯列表
 * @param appId 游戏ID
 * @returns 资讯数据列表
 */
export const getInfoData = async (
  appId: string
): Promise<{
  more: { jump_url: string };
  tabs: { name: string; list: InfoItem[] }[];
}> => {
  const resp = await fetch(
    `/api/pallas/static/template_data/${appId}/info.json`
  );
  const infoJson: {
    more: { jump_url: string };
    tabs: { name: string; items: string[][] }[];
  } = resp.ok ? await resp.json() : null;
  if (infoJson) {
    const tabs = (infoJson.tabs || []).map(tab => {
      const [prop1, prop2, prop3, prop4] = tab.items[0];
      return {
        name: tab.name,
        list: tab.items.slice(1).map(item => ({
          [prop1 as 'title']: item[0],
          [prop2 as 'release_time']: item[1],
          [prop3 as 'jump_url']: item[2],
          [prop4 as 'abstract']: item[3]
        }))
      };
    });
    return { more: infoJson.more, tabs };
  }
  return { more: { jump_url: '' }, tabs: [] };
};
