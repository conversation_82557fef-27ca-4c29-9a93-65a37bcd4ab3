.popbox-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 506;
  background-color: transparent;
  border: 0;
  pointer-events: none;
  &.popbox-show {
    pointer-events: auto;
    .popbox-preview-mask {
      opacity: 1;
    }
    .popbox-preview-bd {
      opacity: 1;
      animation: galleryPopboxAniShow 0.2s ease backwards;
    }
    .popbox-preview-close {
      animation: gallerythumbBtnShow 0.2s 0.3s ease-out backwards;
    }
    .gallerythumb-banner-navigation-btn {
      animation: gallerythumbBtnShow 0.2s 0.3s ease-out backwards;
    }
  }
  &.popbox-hide {
    .popbox-preview-bd {
      opacity: 0;
      animation: galleryPopboxAniHide 0.2s ease backwards;
    }
    .popbox-preview-mask {
      transition-delay: 0.25s;
    }
    .popbox-preview-close {
      opacity: 0;
    }
    .gallerythumb-banner-navigation-btn {
      opacity: 0;
    }
  }
  .popbox-preview-close {
    position: absolute;
    top: 40px;
    right: 1rem;
    width: 80px;
    height: 80px;
    cursor: pointer;
    z-index: 2;
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 27px;
      height: 27px;
      margin: -14px 0 0 -14px;
      -webkit-mask-image: url(./icon-popbox-close.png);
      mask-image: url(./icon-popbox-close.png);
      -webkit-mask-repeat: no-repeat;
      mask-repeat: no-repeat;
      -webkit-mask-size: 100% auto;
      mask-size: 100% auto;
      -webkit-mask-position: center center;
      mask-position: center center;
      background-color: #ebeae8;
      -webkit-transition: background-color 0.25s ease;
      transition: background-color 0.25s ease;
    }
    &:hover {
      &::after {
        background-color: #fff;
      }
    }
  }
  .popbox-main {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .popbox-inner {
    width: 100%;
    height: 100%;
  }
}
.popbox-preview-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: rgba(11, 11, 11, 0.8);
  opacity: 0;
}
.popbox-preview-inner {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.gallerythumb-wrapper-cont {
  width: 100vw;
  height: 100vh;
  position: relative;
}
.gallerythumb-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  width: calc(100vw - 400px);
  height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 500px;
  min-height: 300px;
}

.gallerythumb-banner-list {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.25s ease;
}
.gallerythumb-banner-navigation-btn {
  width: 80px;
  height: 184px;
  position: absolute;
  top: 50%;
  margin-top: -92px;
  cursor: pointer;
  &.prev {
    left: 1rem;
    &::after {
      transform: rotate(-180deg);
    }
  }
  &.next {
    right: 1rem;
  }
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 21px;
    height: 40px;
    margin: -20px 0 0 -11px;
    -webkit-mask-image: url(./icon-popbox-arrow.png);
    mask-image: url(./icon-popbox-arrow.png);
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 100% auto;
    mask-size: 100% auto;
    mask-position: center center;
    background-color: #ebeae8;
    transition:
      background-color 0.25s ease,
      transform 0.25s ease;
  }
  &:hover {
    &::after {
      background-color: var(--color-white);
    }
    &.prev {
      &::after {
        transform: rotate(-180deg) translateX(6px);
      }
    }
    &.next {
      &::after {
        transform: translateX(6px);
      }
    }
  }
  &.disabled {
    cursor: default;
    &:hover {
      &::after {
        background-color: #ebeae8;
      }
      &.prev {
        &::after {
          transform: rotate(-180deg) translateX(0);
        }
      }
      &.next {
        &::after {
          transform: translateX(0);
        }
      }
    }
    &::after {
      opacity: 0.6;
    }
  }
}

@keyframes galleryPopboxAniShow {
  0% {
    transform: translateZ(20px) scale(0.43);
    opacity: 0;
  }
  to {
    transform: translateZ(20px) scale(1);
    opacity: 1;
  }
}
@keyframes galleryPopboxAniHide {
  0% {
    transform: translateZ(20px) scale(1);
    opacity: 1;
  }
  to {
    transform: translateZ(20px) scale(0.43);
    opacity: 0;
  }
}
@keyframes gallerythumbBtnShow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes gallerythumbBtnHide {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.gallerythumb-banner-info {
  width: 688px;
  height: 146px;
  background: rgba(20, 20, 20, 0.9);
  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.4);
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 16px;
  box-sizing: border-box;
  opacity: 0;
  overflow: hidden;
  transform: translate3d(-50%, 10px, 0);
  pointer-events: none;
  transition:
    opacity 0.25s ease-out,
    transform 0.25s ease-out;
  .gallery-thumbcard-heatnum {
    justify-content: center;
    margin-bottom: 10px;
  }
}
.gallerythumb-banner-title {
  font-size: var(--font-size-l);
  font-weight: 400;
  line-height: 22px;
  text-align: center;
  color: var(--color-white);
  margin-bottom: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.gallerythumb-banner-btnwrap {
  display: flex;
  align-items: center;
  justify-content: center;
}
.gallerythumb-banner-btn {
  width: calc(100% - 16px);
  height: 48px;
  margin: 0 0 0 16px;
  border: 0;
  border-radius: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-s);
  font-weight: 700;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  background-color: #36373b;
  transition:
    color 0.25s ease,
    background-color 0.25s ease;
  &:first-child {
    margin: 0;
  }
  &:hover {
    background-color: #5e5f62;
    color: #fff;
    .gallerythumb-banner-icon {
      opacity: 1;
    }
  }
  &.disabled {
    cursor: default;
    color: rgba(255, 255, 255, 0.8);
    background-color: rgba($color: #36373b, $alpha: 0.35);
    pointer-events: none;
    .gallerythumb-banner-icon {
      opacity: 0.5;
    }
    .gallerythumb-banner-btn-text {
      color: rgba(255, 255, 255, 0.5);
    }
    &:hover {
      color: rgba(255, 255, 255, 0.5);
      background-color: rgba($color: #36373b, $alpha: 0.35);
      .gallerythumb-banner-icon {
        opacity: 0.8;
      }
    }
  }
}
.gallerythumb-banner-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  vertical-align: middle;
  margin-right: 4px;
  background-image: url(./icon-wallpaper.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: center center;
  opacity: 0.8;
  transition: opacity 0.25s ease;
}
.gallerythumb-banner-poster {
  overflow: hidden;
}
.gallerythumb-banner-poster img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: calc(100vh - 200px);
  display: block;
  user-select: none !important;
}

.gallerythumb-banner-item {
  position: relative;
  &:hover {
    .gallerythumb-banner-info {
      opacity: 1;
      transform: translate3d(-50%, 0, 0);
      pointer-events: auto;
    }
  }
}
.gallerythumb-banner-btn-text {
  display: inline-block;
  line-height: 24px;
}
@media screen and (max-width: 1100px) {
  .gallerythumb-banner-info {
    width: 588px;
    zoom: 0.65;
  }
}
@media screen and (max-width: 1000px) {
  .gallerythumb-banner-info {
    width: 488px;
    zoom: 0.6;
  }
}
@media screen and (max-width: 800px) {
  .gallerythumb-banner-info {
    width: 488px;
    zoom: 0.5;
  }
}
@media screen and (max-height: 500px) {
  .gallerythumb-banner-info {
    width: 588px;
    zoom: 0.7;
  }
  .gallerythumb-banner-poster img {
    min-height: 200px;
  }
}
@media screen and (max-height: 400px) {
  .gallerythumb-banner-info {
    width: 488px;
    zoom: 0.6;
  }
}
@media screen and (max-height: 300px) {
  .gallerythumb-banner-info {
    width: 488px;
    zoom: 0.5;
  }
}
.gallerythumblist-enter-active,
.gallerythumblist-leave-active {
  transition: opacity 0.25s ease;
}
.gallerythumblist-enter-from,
.gallerythumblist-leave-to {
  opacity: 0;
}

// 弹窗图标增加
.gallery-thumbcard-intro-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.gallery-thumbcard-intro {
  display: flex;
  align-items: center;
  opacity: 0.6;
  margin: 0 0 0 20px;
  line-height: 24px;
}
.gallery-thumbcard-intro-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 2px;
  background-image: url(./icon-heatnum.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: center center;
  &.status-heatnum {
    background-image: url(./icon-heatnum.png);
  }
  &.status-picturesize {
    background-image: url(./icon-picturesize.png);
  }
  &.status-filesize {
    background-image: url(./icon-filesize.png);
  }
}
.gallery-thumbcard-intro-text {
  color: var(--color-white);
  font-size: 15px;
  font-weight: 400;
}
