import { isWeGameMain, service } from '@tencent/wegame-web-sdk';

export const uuid = () => {
  let d = new Date().getTime();
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    function (c) {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c === 'x' ? r : (r & 0x7) | 0x8).toString(16);
    }
  );
  return uuid;
};

// style 0: center 1: tile 2: stretch 3: keepaspect 4: croptofit 5: span
export async function setWallpaper(
  url: string,
  style: 0 | 1 | 2 | 3 | 4
): Promise<void> {
  if (!isWeGameMain) {
    return;
  }

  const requestId = uuid();

  return new Promise((resolve, reject) => {
    service.call_service('Srv_Lua_SetWallPaper', {
      id: requestId,
      url,
      style
    });

    service.listen_broadcast('Msg_<PERSON><PERSON>_SetWallPaperProgress', (res: any) => {
      if (res.progress === 100) {
        resolve();
        return;
      }
      if (res.errcode) {
        reject();
      }
    });
  });
}

function getFileName(url: string) {
  return /\/([^/]+)$/.exec(url)![1];
}

async function downloadImageOutClient(url: string, filename: string) {
  const response = await fetch(url);

  const blobImage = await response.blob();

  const href = URL.createObjectURL(blobImage);

  const anchorElement = document.createElement('a');
  anchorElement.href = href;
  anchorElement.download = filename;

  document.body.appendChild(anchorElement);
  anchorElement.click();

  document.body.removeChild(anchorElement);
  URL.revokeObjectURL(href);
}

function selectDownloadDir() {
  const requestId = uuid();

  service.call_service('Srv_TGPConfigMgr_SelectDir', {
    req_id: requestId,
    dir_path: '%USERPROFILE%\\Downloads'
  });
  return new Promise(resolve => {
    service.listen_broadcast('Msg_TGPConfigMgr_SelectDirResult', (res: any) => {
      if (res.req_id !== requestId) {
        return;
      }
      resolve(res.dir_path);
    });
  });
}

async function downloadImageInWeGame(
  url: string,
  filename: string
): Promise<void> {
  const dirPath = await selectDownloadDir();
  const requestId = uuid();
  return new Promise((resolve, reject) => {
    if (!dirPath) {
      reject();
      return;
    }
    service.call_service('Srv_Lua_DownloadWallPaper', {
      id: requestId,
      url,
      dir_path: dirPath,
      name: filename
    });

    service.listen_broadcast(
      'Msg_Lua_DownloadWallPaperProgress',
      (res: any) => {
        if (res.progress === 100) {
          return resolve();
        }
        if (res.errcode) {
          reject();
        }
      }
    );
  });
}

export async function downloadImage(
  url: string,
  filename?: string,
  ext?: string
) {
  const fileExt = ext ?? url.split('.').pop();
  const saveFilename = filename
    ? `${filename}-${Date.now()}.${fileExt}`
    : getFileName(url);
  if (isWeGameMain) {
    await downloadImageInWeGame(url, saveFilename);
  } else {
    await downloadImageOutClient(url, saveFilename);
  }
}
