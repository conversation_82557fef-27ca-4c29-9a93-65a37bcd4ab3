import { baseRequest } from '@tencent/wegame-web-sdk';

export interface WallpaperItem {
  create_time: string;
  file_id: string;
  height: number;
  popularity: number;
  preview: string;
  size: number;
  theme_color: string;
  thumbnail: string;
  title: string;
  url: string;
  width: number;
}

/**
 * 获取某游戏下的壁纸列表
 */
export const getWallpaperList = async ({
  gameId,
  pageNo,
  pageSize
}: {
  gameId: string;
  pageNo: number;
  pageSize: number;
}): Promise<{
  totalSize: number;
  wallpapers: WallpaperItem[];
}> => {
  try {
    const resp = await baseRequest<{
      total_size: number;
      wallpapers: WallpaperItem[];
    }>({
      url: '/api/v1/wegame.rail.game.GameMedia/GetWallpaper',
      method: 'POST',
      data: { game_id: gameId, page_no: pageNo, page_size: pageSize }
    });
    if (resp.total_size) {
      return {
        totalSize: resp.total_size,
        wallpapers: resp.wallpapers
      };
    }
  } catch (error) {
    console.error(`fetch GetWallpaper failed:`, error);
  }
  return {
    totalSize: 0,
    wallpapers: []
  };
};

export const likeWallpaper = async ({
  gameId,
  fileId
}: {
  gameId: string;
  fileId: string;
}) => {
  try {
    const resp = await baseRequest({
      url: '/api/v1/wegame.rail.game.GameMedia/LikeWallpaper',
      method: 'POST',
      data: { game_id: gameId, file_id: fileId }
    });

    return resp.result?.error_code === 0;
  } catch (error) {
    console.error(`fetch LikeWallpaper failed:`, error);
  }
  return false;
};
