<template>
  <div
    :class="`icon-hover-tips position-${position || 'tm'}`"
  >
    <span class="icon-hover-ele"></span>
    <div v-if="text" class="icon-hover-info" v-html="text"></div>
    <div v-if="$slots.tips" class="icon-hover-info">
      <slot name="tips"></slot>
    </div>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
import './index.scss';

defineProps<{
  text?: string;
  position?: 'tm' | 'tl' | 'tr' | 'bm' | 'bl' | 'br';
}>();
</script>
