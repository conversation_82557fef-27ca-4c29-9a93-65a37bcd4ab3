@charset "utf-8";

.icon-hover-tips {
  --hover-icon-size: 0.16rem;
  min-width: var(--hover-icon-size);
  min-height: var(--hover-icon-size);
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.12rem;
}
.icon-hover-ele {
  width: var(--hover-icon-size);
  aspect-ratio: 1/1;
  -webkit-mask: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAADq6urs7Ozr6+vq6urr6+vp6enr6+vp6enq6urq6urq6urq6urr6+vr6+vs7Ozs7Ozp6enr6+vq6up5PlEKAAAAE3RSTlMA3xDvvyCQQIBwr59gz39QMKCQhv+WWAAAASRJREFUOMttUwuWgyAMZIRGQdC2c/+7rqwGiDXvoUAmyZCPG2SWBKAEmd2DTJsn6YH6g7zu+ujpZZ/+ofMHRLTmgX6ZXJcMhsHJ6ziqWq8S8Rr0yy+phVCj0PUWES5+TEolJpSUXTOMZwAN9vas0lxP3tfdl/micujXRXB8W5AaG7iOK/m+cN3Fkd+GV0shNdfCuS5lGM5d7ICd4oL6a1LYriYmV/xNv3FIi4cD7lUZ04YfQKGPzgJMiHw+tYcoLhmSQR+tJIMT7gaQTRNS6hpvsum1tWbkrMizAFoRFQnJUP5qUTsH6LY3QmRodx+EDkiMarY9Edia4YQnxGbbOumh1wTjYBzHbOfwPikRxDqfo7eL7yXrTgR9eIc5tOMfCoBkxv8PGgcL+HoEw+cAAAAASUVORK5CYII=);
  -webkit-mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  background-color: rgba($color: #fff, $alpha: 0.6);
  display: block;
  transition: background-color 0.2s linear;
}
.icon-hover-tips {
  .icon-hover-info{
    position: absolute;
    right: 0.2rem;
    top: 0.1rem;
    background-color: rgba(0, 0, 0, 0.8);
    display: block;
    color: rgba($color: #fff, $alpha: 0.7);
    z-index: 10;
    padding: 0.05rem 0.1rem;
    font-size: 0.12rem;
    border: 1px solid rgba($color: #fff, $alpha: 0.2);
    max-width: 2rem;
    line-height: 1.6;
    width: max-content;
    font-weight: normal;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s linear;
    white-space: pre-wrap;
  }
 
  &.position-tm {
    .icon-hover-info {
      left: 50%;
      top: auto;
      bottom: calc(100% + 0.03rem);
      transform: translate3d(-50%, 0, 0);
    }
  }

  &.position-tl {
    .icon-hover-info {
      top: auto;
      right: calc(100% + 0.03rem);
      bottom: calc(100% + 0.03rem);
    }
  }
  &.position-tr {
    .icon-hover-info {
      top: auto;
      right: auto;
      left: calc(100% + 0.03rem);
      bottom: calc(100% + 0.03rem);
    }
  }
  &.position-bm {
    .icon-hover-info {
      left: 50%;
      top: calc(100% + 0.03rem);
      transform: translate3d(-50%, 0, 0);
    }
  }
  &.position-bl {
    .icon-hover-info {
      right: calc(100% + 0.03rem);
      top: calc(100% + 0.03rem);
    }
  }
  &.position-lm {
    .icon-hover-info {
      right: calc(100% + 0.03rem);
      top: 50%;
      transform: translate3d(0, -50%, 0);
    }
  }
  &.position-rm {
    .icon-hover-info {
      left: calc(100% + 0.03rem);
      top: 50%;
      transform: translate3d(0, -50%, 0);
    }
  }
  &.position-br {
    .icon-hover-info {
      right: auto;
      left: calc(100% + 0.03rem);
      top: calc(100% + 0.03rem);
    }
  }
  &:hover {
    .icon-hover-ele {
      background-color: rgba($color: #fff, $alpha: 1);
    }
    .icon-hover-info {
      opacity: 1;
    }
  }
}
