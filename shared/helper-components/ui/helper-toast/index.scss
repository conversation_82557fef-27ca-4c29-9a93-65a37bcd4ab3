@charset "utf-8";
.helper-toast {
  --toast-bottom-offset: 50px;
  --toast-color-fill: linear-gradient(
    to right,
    transparent 0%,
    rgba(0, 0, 0, 0.8) 0.6rem,
    rgba(0, 0, 0, 0.8) calc(100% - 0.6rem),
    transparent 100%
  );
  --toast-color-text: rgba(255, 255, 255, 0.8);
  --toast-font-size: 0.12rem;

  position: fixed;
  inset: 0;
  z-index: 600;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: calc(
    var(--helper-launcher-height) + var(--toast-bottom-offset)
  );
  padding-left: var(--helper-sidebar-on-width, 0);
  transition: var(--transition-padding-content);
}
.helper-toast-mask {
  position: absolute;
  inset: 0;
  z-index: 2;
  pointer-events: all;
}
.helper-toast-ele {
  min-width: 1.6rem;
  background: var(--toast-color-fill);
  font-size: var(--toast-font-size);
  color: var(--toast-color-text);
  display: flex;
  align-items: flex-start;
  position: relative;
  padding: 0.08rem 0.6rem 0.14rem 0.6rem;
  max-width: 5rem;
  pointer-events: all;
  animation: toastShowAndHide 3s ease-in-out 0s 1 both;
  line-height: 1.6;
}
.helper-toas-center {
  .helper-toast-ele {
    text-align: center;
  }
}
.helper-toast-icon {
  width: 0.24rem;
  min-width: 0.24rem;
  height: 0.24rem;
  font-size: 0;
  margin: 0.05rem 0.1rem 0 0;
  background: url('../../assets/images/icon-info.png') no-repeat center;
  background-size: contain;
}
.toast-icon-error {
  background: url('../../assets/images/icon-error.png') no-repeat center;
  background-size: contain;
}
.toast-icon-success {
  background: url('../../assets/images/icon-success.png') no-repeat center;
  background-size: contain;
}
.helper-toast-text {
  padding: 0.06rem 0 0 0;
}

.toast-position-bottom {
  &.helper-toast {
    justify-content: flex-end;
  }
}

@keyframes toastShowAndHide {
  0% {
    opacity: 0;
    transform: translate3d(0, 30%, 0);
  }
  15% {
    opacity: 1;
    transform: none;
  }
  90% {
    opacity: 1;
    transform: none;
  }
  100% {
    opacity: 0;
    transform: translate3d(0, 40%, 0);
  }
}
