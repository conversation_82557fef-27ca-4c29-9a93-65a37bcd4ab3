import { createApp, App } from 'vue';
import { withInstall } from '../../utils/with-install';
import Toast from './index.vue';

type MessageOrOptions = string | ({ message: string } & Record<string, any>);

let toastInstance: (App<Element> & { el?: HTMLElement }) | null = null;

const createToastInstance = (messageOrOptions: MessageOrOptions) => {
  if (toastInstance) {
    close();
  }

  const options = normalizeOptions(messageOrOptions);

  toastInstance = createApp(Toast, {
    ...options,
    onClose() {
      close();
    }
  });
  const el = document.createElement('div');
  toastInstance.el = el;
  toastInstance.mount(el);
  document.body.appendChild(el);
};

const close = () => {
  if (!toastInstance) {
    return;
  }
  toastInstance.unmount();
  toastInstance.el?.remove();
  toastInstance = null;
};

const normalizeOptions = (messageOrOptions: MessageOrOptions) => {
  return typeof messageOrOptions === 'string'
    ? { message: messageOrOptions }
    : messageOrOptions;
};

const createShortcut = (
  messageOrOptions: MessageOrOptions,
  defaultType: 'text' | 'warning' | 'info' | 'success' | 'error'
) => {
  createToastInstance({
    ...normalizeOptions(messageOrOptions),
    type: defaultType
  });
};

 const extra = {
  info: (options: MessageOrOptions) => createShortcut(options, 'info'),
  warning: (options: MessageOrOptions) => createShortcut(options, 'warning'),
  success: (options: MessageOrOptions) => createShortcut(options, 'success'),
  error: (options: MessageOrOptions) => createShortcut(options, 'error'),
  text: (options: MessageOrOptions) => createShortcut(options, 'text')
};

const HelperToast = withInstall(Toast, {}, extra);

export default HelperToast;