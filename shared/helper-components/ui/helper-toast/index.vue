<template>
  <div :class="`helper-toast toast-position-${position}`">
    <div
      class="helper-toast-ele"
      :style="`animation-duration: ${duration || 3000}ms;`"
      @animationend="onAnimationEnd"
    >
      <template v-if="!$slots.custom">
        <span
          v-if="icon"
          :class="`helper-toast-icon toast-icon-${icon}`"
        ></span>
        <div v-if="message" class="helper-toast-text">
          {{ message }}
        </div>
        <div v-if="$slots.default" class="helper-toast-text">
          <slot></slot>
        </div>
      </template>
      <div v-if="$slots.custom" class="helper-toast-cont">
        <slot name="custom"></slot>
      </div>
    </div>
    <div v-if="modal" class="helper-toast-mask"></div>
  </div>
</template>
<script setup lang="ts">
import './index.scss';
import { computed } from 'vue';

const emits = defineEmits<{
  (e: 'close'): void;
}>();

const props = withDefaults(
  defineProps<{
    type?: 'info' | 'warning' | 'success' | 'error' | 'text';
    modal?: boolean; // 是否阻止点击
    message: string;
    description?: string;
    position?: 'center' | 'bottom' | 'auto';
    duration?: number; // toast显示持续时间
  }>(),
  {
    type: 'info',
    description: '',
    modal: false,
    position: 'center',
    duration: 3000
  }
);

const ICON_MAPPING = {
  info: 'info',
  success: 'success',
  error: 'error'
};

const icon = computed(() => {
  return ICON_MAPPING[props.type];
});

const onAnimationEnd = () => {
  emits('close');
}
</script>
