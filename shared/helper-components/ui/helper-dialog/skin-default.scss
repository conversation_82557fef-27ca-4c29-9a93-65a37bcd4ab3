@charset "utf-8";
.helper-dialog {
  --dialog-color-text-primary: rgba(255, 255, 255, 1);
  --dialog-color-text-secondary: rgba(255, 255, 255, 0.8);
  --dialog-header-height: 0.36rem;
  --dialog-content-min-width: 4.2rem;
  --dialog-content-max-width: 4.2rem;
  --dialog-button-color-primary: rgba(255, 255, 255, 0.75);
  --dialog-button-color-normal: rgba(255, 255, 255, 0.75);
  --dialog-button-fill-primary: rgba(255, 143, 0, 0.25);
  --dialog-button-fill-normal: rgba(255, 255, 255, 0.15);
  --dialog-button-border-primary: 0.01rem solid rgba(255, 143, 0, 0.15);
  --dialog-button-border-normal: 0.01rem solid rgba(255, 255, 255, 0.15);
}

.helper-dialog-ele {
  min-width: var(--dialog-content-min-width);
  background: rgba(0, 0, 0, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.helper-dialog-mask {
  background-color: rgba($color: #000000, $alpha: 0.75);
}

.helper-dialog-cont {
  padding: 0.32rem;
  min-width: var(--dialog-content-min-width);
  max-width: var(--dialog-content-max-width);
  &:nth-last-child(2) {
    padding-bottom: 0.16rem;
  }
}
.helper-dialog-icon {
  margin: 0.05rem 0.12rem 0 0;
  background: url('../../assets/images/icon-info.png') no-repeat center;
  background-size: contain;
}

.helper-dialog-title {
  font-size: 0.18rem;
  font-weight: 700;
  color: var(--dialog-color-text-primary);
  padding: 0 0 0.06rem 0;
  line-height: 1.5;
}
.helper-dialog-icon + .helper-dialog-main {
  .helper-dialog-title {
    &:only-child {
      padding: 0;
      margin: 0.14rem 0 0 0;
    }
  }
}

.helper-dialog-desc {
  font-size: 0.12rem;
  color: var(--dialog-color-text-secondary);
  line-height: 1.5;
}

.helper-dialog-header {
  height: var(--dialog-header-height);
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 0.01rem;
    background-color: rgba(255, 255, 255, 0.15);
  }
}

.helper-dialog-header-title {
  font-size: 0.16rem;
  font-weight: bold;
  height: var(--dialog-header-height);
  padding: 0 0 0 0.16rem;
}

.helper-dialog-header-close {
  &::before {
    content: '';
    width: 0.16rem;
    height: 0.16rem;
    background: url('../../assets/images/icon-close.png') no-repeat center;
    background-size: contain;
    display: block;
    opacity: 0.65;
    transition: opacity 0.2s linear;
  }
  &:hover::after {
    opacity: 1;
  }
  &::before {
    content: '';
    background: url('../../assets/images/icon-close.png') no-repeat center;
    background-size: contain;
  }
}

.helper-dialog-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.16rem 0.12rem 0.24rem 0.12rem;
  .helper-dialog-button {
    margin: 0 0.12rem;
  }
}

.helper-dialog-button {
  height: 0.3rem;
  color: rgba($color: #fff, $alpha: 0.75);
  padding: 0 0.12rem;
  min-width: 0.8rem;
  font-size: 0.14rem;
  transition:
    border-color 0.2s linear,
    background-color 0.2s linear,
    color 0.2s linear;
}

.helper-dialog-button {
  &.primary {
    .helper-dialog-button-bg {
      background: var(--dialog-button-fill-primary);
      border: var(--dialog-button-border-primary);
      opacity: 0.75;
    }
  }
  &.normal {
    .helper-dialog-button-bg {
      background: var(--dialog-button-fill-normal);
      border: var(--dialog-button-border-normal);
      opacity: 0.5;
    }
  }
  &:hover {
    .helper-dialog-button-bg {
      opacity: 1;
    }
  }
}

.helper-dialog-ele {
  animation: dialogShow 0.3s ease-in-out 0.2s 1 both;
}
.helper-dialog-mask {
  animation: dialogMaskShow 0.3s linear 0s 1 both;
}

@keyframes dialogMaskShow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes dialogShow {
  0% {
    opacity: 0;
    transform: translate3d(0, 15%, 0);
  }
  100% {
    opacity: 1;
  }
}
