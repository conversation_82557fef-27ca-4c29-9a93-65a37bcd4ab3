@charset "utf-8";
.helper-dialog {
  position: fixed;
  inset: 0;
  z-index: 510;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: var(--helper-launcher-height, 0);
  padding-left: var(--helper-sidebar-on-width, 0);
  transition: var(--transition-padding-content);
}

.helper-dialog-ele {
  min-width: var(--dialog-content-min-width, 0);
  position: relative;
  z-index: 10;
}

.helper-dialog-mask {
  position: absolute;
  inset: 0;
  z-index: 2;
}

.helper-dialog-cont {
  display: flex;
  align-items: flex-start;
}
.helper-dialog-icon {
  width: 0.48rem;
  min-width: 0.48rem;
  height: 0.48rem;
  font-size: 0;
  margin: 0.05rem 0.12rem 0 0;
}
.helper-dialog-main {
  display: flex;
  flex-direction: column;
}

.helper-dialog-title {
  font-size: 0.18rem;
  font-weight: 700;
  padding: 0 0 0.06rem 0;
  line-height: 1.5;
}
.helper-dialog-icon + .helper-dialog-main {
  .helper-dialog-title {
    &:only-child {
      padding: 0;
      margin: 0.14rem 0 0 0;
    }
  }
}

.helper-dialog-desc {
  font-size: 0.12rem;
}

.helper-dialog-header {
  position: relative;
  display: flex;
  align-items: center;
}

.helper-dialog-cont-custom {
}
.helper-dialog-footer {
}
.helper-dialog-header-title {
  font-size: 0.16rem;
  font-weight: bold;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 0 0 0.16rem;
}
.helper-dialog-header-close {
  width: 0.36rem;
  height: 0.36rem;
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.helper-dialog-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.16rem 0.12rem 0.24rem 0.12rem;
  .helper-dialog-button {
    margin: 0 0.12rem;
  }
}

.helper-dialog-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.12rem;
  min-width: 0.8rem;
  font-size: 0.14rem;
  cursor: pointer;
  position: relative;
  transition:
    border-color 0.2s linear,
    background-color 0.2s linear,
    color 0.2s linear;
  .helper-dialog-button-bg {
  }
}
.helper-dialog-button-text {
  position: relative;
  z-index: 4;
}
.helper-dialog-button-bg {
  position: absolute;
  inset: 0;
  z-index: 1;
  transition: opacity 0.2s linear;
}

.helper-dialog-ele {
  animation: dialogShow 0.3s ease-in-out 0.2s 1 both;
}
.helper-dialog-mask {
  animation: dialogMaskShow 0.3s linear 0s 1 both;
}

@keyframes dialogMaskShow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes dialogShow {
  0% {
    opacity: 0;
    transform: translate3d(0, 15%, 0);
  }
  100% {
    opacity: 1;
  }
}
