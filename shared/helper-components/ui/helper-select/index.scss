.helper-select {
  position: relative;
  height: 0.34rem;
  outline: 1px solid rgba($color: #fff, $alpha: 0.2);
  &:hover {
    outline-color: rgba($color: #fff, $alpha: 0.4);
  }
}
.helper-select-input {
  height: 0.34rem;
  padding: 0 0.3rem 0 0.15rem;
  font-size: 0.14rem;
  min-width: 0.6rem;
  color: #fff;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  &::after {
    content: '';
    width: 12px;
    height: 8px;
    position: absolute;
    top: 50%;
    right: 10px;
    margin-top: -4px;
    // background: url("assets/svg/icon-arrow.svg") no-repeat 50% 50%;
    background-size: 100% auto;
    display: block;
  }
  height: 100%;
  font-size: 0.14rem;
  font-weight: bold;
  padding: 0 0.4rem 0 0.12rem;
  color: var(--helper-color-text-2);
  &::after {
    width: 0.16rem;
    height: 0.09rem;
    right: 0.15rem;
    background: url('assets/images/icon-arrow.png') no-repeat;
    background-position: 0 100%;
    background-size: auto 100%;
    opacity: 0.6;
  }
}
.helper-select-dropdown {
  position: absolute;
  top: 0.34rem;
  left: -1px;
  right: -1px;
  min-width: 100%;
  pointer-events: none;
  transform-origin: 50% 0%;
  animation: menuHoverAnim 0.3s cubic-bezier(0.64, 0.62, 0.13, 1.02) 0.1s 1 both;
}
.helper-dropdown-bd {
  border: 1px solid rgba($color: #eaebeb, $alpha: 0.4);
  background: var(--helper-color-fill-1);
  backdrop-filter: blur(20px);
}
.helper-dropdown-menu {
  height: 0.36rem;
  padding: 0 0.15rem;
  font-size: 0.14rem;
  color: rgba($color: #fff, $alpha: 0.6);
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
  animation: menuItemHoverAnim 0.2s cubic-bezier(0.64, 0.62, 0.13, 1.02) 0.15s 1
    both;
}
.helper-dropdown-menu {
  &:hover {
    background-color: rgba($color: #fff, $alpha: 0.1);
    color: #fff;
  }
  &.current {
    background-color: rgba($color: #fff, $alpha: 0.15);
    color: #fff;
  }
}

.helper-select--hover {
  .helper-select-dropdown {
    pointer-events: auto;
  }
}

.helper-select--scroll {
  .helper-dropdown-bd {
    max-height: 0.3rem;
    overflow: auto;
  }
}
