.general-dialog-box {
    --page-gap-left-value: 50px;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba($color: #000000, $alpha: 0.7);
    z-index: 100;
}

.general-dialog {
    position: relative;
    min-width: 400px;
    min-height: 160px;
    padding: 52px 24px 40px;
    background: #131417;
    border-top: 2px solid #FF8F00;
}


.general-dialog-tit {
    text-align: center;
    font-size: 20px;
    color: #EAEBEB;
    font-weight: bold;
    margin-bottom: 10px;
}

.general-dialog-close-btn {
    position: absolute;
    right: 8px;
    top: 6px;
    width: 38px;
    height: 38px;
    opacity: 0.8;
    background: url(../images/close-icon.png) no-repeat;
    background-size: contain;
    transition: opacity 0.2s;
    cursor: pointer;

    &:hover {
        opacity: 1;
    }
}

.general-dialog-txt {
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #818C8F;
}

.general-dialog-btn {
    width: 120px;
    height: 40px;
    margin: 40px auto 0;
    border: 2px solid rgb(255, 143, 0, 0.45);
    color: #FF8F00;
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 40px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
    &:hover{
        opacity: 1;
    }
}

.sidebar-on .general-dialog,
.sidebar-off .general-dialog {
    transition: transform 0.2s ease-in-out;
}

.sidebar-on {
    .general-dialog {
        // left: var(--page-gap-left-value);
        transform: translateX(var(--page-gap-left-value));
    }
}


// 切换背景图动画 start
.general-dialog-enter-active,
.general-dialog-leave-active {
    transition: opacity 0.5s;

    &.general-dialog-box {
        transition: opacity 0.5s, transform 0.5s;
    }
}

.general-dialog-leave-from,
.general-dialog-enter-to {

    &.general-dialog-box {
        opacity: 1;
    }
}

.general-dialog-leave-to,
.general-dialog-enter-from {

    &.general-dialog-box{
        opacity: 0;
    }
}

// 切换背景图动画 end