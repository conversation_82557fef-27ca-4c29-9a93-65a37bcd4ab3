.progress-frame {
    // display: none;
    --page-gap-left-value: 200px;
    z-index: 100;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0.6);
}

.progress-panel {
    position: fixed;
    width: 500px;
    height: 300px;
    padding-top: 48px;
    border-top: 2px solid #FF8F00;
    background: #131417;
    border-radius: 2px;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.progress-close-button {
    position: absolute;
    right: 8px;
    top: 6px;
    width: 38px;
    height: 38px;
    background: url(../images/close-icon.png) no-repeat;
    background-size: contain;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.2s;

    &:hover {
        opacity: 1;
    }
}

.progress-title {
    color: #FFF;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    line-height: normal;
}

.progress-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
}

.progress-text {
    position: absolute;
    color: #FFF;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
}

.progress-circle {
    width: 160px;
    height: 160px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    svg {
        transform: rotate(-90deg);
    }
    
    circle.progress {
        stroke-linecap: round;
        transition: stroke-dashoffset 0.8s ease;
    }
    
}


.sidebar-on .progress-panel,
.sidebar-off .progress-panel {
    transition: left 0.2s ease-in-out;
}

.sidebar-on {
    .progress-panel {
        left: var(--page-gap-left-value)
    }
}



.progress-enter-active,
.progress-leave-active {
    transition: opacity 0.5s;
}

.progress-leave-from,
.progress-enter-to {
    opacity: 1;
}

.progress-leave-to,
.progress-enter-from {
    opacity: 0;
}