<template>
  <transition name="progress">
    <div v-if="progressFrameVisible" class="progress-frame">
      <div class="progress-panel">
        <!-- <div class="progress-close-button" @click="close"></div> -->
        <div class="progress-title">工具加载中，请稍后...</div>
        <div class="progress-box">
          <div class="progress-circle">
            <!-- 增加viewBox尺寸，用于支持内容加投影不被裁切 -->
            <svg width="240" height="240" viewBox="0 0 244 244" fill="none">
              <circle
                class="background"
                cx="120"
                cy="120"
                r="116"
                stroke-width="8"
                fill="none"
                opacity="0.1"
                stroke="#fff"
              />
              <!-- 支持渐变 -->
              <defs v-if="linearEndColor">
                <linearGradient
                  id="gradient"
                  gradientUnits="userSpaceOnUse"
                  x1="0"
                  y1="120"
                  x2="240"
                  y2="120"
                >
                  <stop
                    offset="0%"
                    :style="`stop-color:${linearStartColor}`"
                  ></stop>
                  <stop
                    offset="100%"
                    :style="`stop-color:${linearEndColor || linearStartColor}`"
                  ></stop>
                </linearGradient>
              </defs>
              <circle
                class="progress"
                cx="120"
                cy="120"
                r="116"
                stroke-width="8"
                :stroke="linearStartColor ? `url(#gradient)` : color"
                fill="none"
                stroke-dasharray="753.6"
                :stroke-dashoffset="753.6 - (progress / 100) * 753.6"
                style="transition: stroke-dashoffset 0.6s"
              />
            </svg>
          </div>
          <div class="progress-text">{{ progress }}%</div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import './assets/scss/progress.scss';

withDefaults(
  defineProps<{
    title?: string;
    progress?: number;
    color?: string;
    text?: string;
    duration?: number;
    progressFrameVisible: Boolean;
    linearStartColor?: string;
    linearEndColor?: string;
  }>(),
  {
    progress: 0,
    color: '#FF8F00'
  }
);

// const emits = defineEmits(['close']);
// function close() {
//   emits('close');
// }
</script>
