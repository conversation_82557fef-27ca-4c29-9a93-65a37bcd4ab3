# Helper Components 组件库文档

## 目录
- [安装与使用](#安装与使用)
- [依赖说明](#依赖说明)
- [组件列表](#组件列表)
  - [适龄提示 (age-tips)](#适龄提示-age-tips)
  - [Astro 侧边栏](#astro-侧边栏)
  - [音频播放器 (audio-player)](#音频播放器-audio-player)
  - [轮播图 (carousel)](#轮播图-carousel)
  - [Feeds页 (feeds-page)](#feeds页-feeds-page)
  - [浮动操作按钮 (floating-action-buttons)](#浮动操作按钮-floating-action-buttons)
  - [单选框 (helper-radio)](#单选框-helper-radio)
  - [直播列表 (live-list)](#直播列表-live-list)
  - [加载动画 (loading)](#加载-loading)
  - [视频弹层 (video-dialog)](#视频弹层-video-dialog)
  - [视频列表 (video-list)](#视频列表-video-list)
  - [视频播放器 (video-player)](#视频播放器-video-player)
  - [壁纸 (wallpaper-preview)](#壁纸游戏图集)
  - [地区选择弹窗 (region-popup)](#地区选择组件)
- [组合式API](#组合式api)
- [工具函数](#工具函数)
- [开发指南](#开发指南)
  - [OSS配置](#OSS配置)
- [数据上报](#数据上报)
- [相关资料](#相关资料)

## 安装与使用

### Git Subtree 命令
```bash
# 添加子仓库
git subtree add --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master --squash

# 更新子仓库
git subtree pull --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master --squash

# 推送变更，尽量不要直接在使用项目里推数据，否则可能会把本地merge不冲突的代码push上来，有的话必须确认全部提交代码
git subtree push --prefix=shared/helper-components ***************:wegame/helper/helper-components.git master
```

### 常见问题
```bash
# 合并冲突解决
fatal: You have not concluded your merge (MERGE_HEAD exists).
git reset --hard

# 工作区有修改时
fatal: working tree has modifications. Cannot add.
git status
```

## 依赖说明
### 核心依赖
- vue
- @vueuse/core
- dayjs
- @tencent/wegame-web-sdk

### 视频相关依赖
- @tencent/super-player (video-list | video-preview组件使用)

## 组件列表

### 适龄提示 (age-tips)

#### 功能
显示游戏的适龄提示信息，根据国家/地区规定展示相应的适龄提示标识和内容。

#### 特性
- 自动根据游戏ID获取适龄提示信息
- 内置默认适龄提示图标
- 响应式设计，适配不同设备

#### Props
| 参数名   | 类型   | 必填 | 默认值 | 说明                   |
|----------|--------|------|--------|------------------------|
| gameId   | string | 是   | -      | 应用程序的唯一标识符    |

#### 使用示例
```vue
<template>
  <age-tips :gameId="gameId" />
</template>

<script>
import AgeTips from 'shared/helper-components/age-tips'

export default {
  components: { AgeTips },
  data() {
    return {
      gameId: '26' // 实际游戏ID
    }
  }
}
</script>
```

#### 注意事项
1. 必须提供有效的gameId才能正确显示适龄提示
2. 组件会自动从WeGame后台配置获取适龄信息
3. 默认显示在页面右下角，可通过CSS调整位置

#### 数据来源
组件数据来自WeGame后台配置的适龄提示系统，包含：
- 适龄等级（如12+、16+、18+等）
- 适龄提示文字
- 相关法律法规要求


### Astro 侧边栏
#### 功能

---

### 音频播放器 (audio-player)
#### 示例助手黑神话悟空
#### 功能
- 音频播放控制
- 音频列表管理
- 歌词显示
- 全局音频状态管理

---

### 轮播图 (carousel)
#### 功能
展示可轮播的内容项

#### Props
| 参数名               | 类型     | 默认值  | 说明                                   |
|----------------------|----------|---------|----------------------------------------|
| appId                | string   | -       | 应用程序的唯一标识符                   |
| navBtnVisible        | boolean  | true    | 是否显示左右翻页箭头                   |
| loop                 | boolean  | true    | 是否支持循环播放                       |
| paginationType       | number   | 3       | 翻页指示器类型                         |
| maxSlides            | number   | 5       | 最多显示的内容项数量                   |
| ossDataName          | string   | -       | OSS表名，优先使用该表名请求数据         |
| ossDataList          | array    | -       | 直接传入的数据，避免再次请求            |
| containerClickedFunc | Function | null    | 容器点击回调函数                        |
| reportExt            | string   | ''      | 数据上报的额外字段                      |


## Feeds页（feeds-page）

## 浮动操作按钮（floating-action-buttons）
支持类型
- QQ群
- 二维码
- 客服

## 单选框（helper-radio）

## 直播列表（live-list）
### 示例助手全境封锁2
### Props

| Name   | Type   | Default     | Description                |
|:------ |:------ |:----------- |:-------------------------- |
| appId  | string | -           | 应用程序的唯一标识符         |
| title  | string | 热门直播     | 标题                       |
| rows   | number | 2           | 行数（x行）                 |
| cols   | number | 3           | 列数（x列）                 |

## 加载（loading）


## 视频弹层（video-dialog）

## 视频列表（video-list）
### Props

| Name                              | Type          | Default     | Description                 |
|:------                            |:------        |:--------    |:--------------------------  |
| appId                             | string        | -           | 应用程序的唯一标识符          |
| title                             | string        | 热门直播     | 标题                        |
| rows                              | number        | 2           | 行数（x行）                  |
| cols                              | number        | 3           | 列数（x列）                  |
| videoDialogOpt.appId              | string        | - | 应用程序的唯一标识符                      |
| videoDialogOpt.list               | VideoItem[]   | []          | 视频弹层视频列表                |
| videoDialogOpt.activeIndex        | number        | 0           | 视频弹层视频列表Index                 |
| videoDialogOpt.slideWidth         | number        | 680         | 视频弹层宽度                          |
| videoDialogOpt.autoPlayNextVideo  | boolean       | false       | 视频弹层视频播完自动播放下一个          |
| videoPlayerOpt.domId              | string        | -           | 播放器DomID                           |
| videoPlayerOpt.vid                | string        | -           | 播放器视频ID                          |
| videoPlayerOpt.play               | boolean       | -           | 控制播放器播放或暂停                   |
| videoPlayerOpt.customClass        | string        | -           | 播放器自定义class                     |
| videoPlayerOpt.offScreenPause     | string        | -           | 播放器离开可视区域关闭播放              |
| videoPlayerOpt.config             | string        | -           | 播放器组件参数透传ISuperPlayerConfig   |
| videoPlayerOpt.volume             | string        | 0           | 播放器声量                            |

## 视频播放器（video-player）

## 壁纸（游戏图集）
### 示例助手 仙剑世界

## 地区选择组件

| <img width="200" src="/uploads/FCDEAA4913E24CF98CDA7FBBDD71CE81/image.png" alt="image.png" /> | <img width="200" src="/uploads/FD97802DFFBE440E9A17FD8C9DA06FDF/image.png" alt="image.png" /> | <img width="200" src="/uploads/E0E752DB3E984FDC8FB28A250F6DA30B/image.png" alt="image.png" /> |
|--------|------|------|

### 地区选择弹窗 (RegionPopup)
#### 功能
提供完整的地区选择功能，支持侧边栏导航和地区选择

#### Props
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| title | string | 否 | '选择战区' | 弹窗标题 |
| show | boolean | 是 | false | 控制弹窗显示状态 |
| selected | RegionItem | 否 | - | 已选地区信息 |
| sidebarSelected | RegionItem | 否 | - | 侧边栏选中项 |
| isShowSelected | boolean | 否 | false | 是否显示已选择区域 |
| sidebarRegionList | RegionItem[] | 否 | - | 侧边栏地区列表 |
| regionList | RegionItem[] | 是 | - | 地区列表 |

#### 使用场景
适用于需要完整地区选择功能的场景，如设置用户所在地、内容分区等

#### 示例代码
见如下 [省份选择弹窗 (ProvincePopup)](#省份选择弹窗-provincepopup) 、[城市选择弹窗 (CityPopup)](#城市选择弹窗-citypopup) 

#### 注意事项
- 支持侧边栏和主区域两级选择
- 内置滚动定位功能，自动滚动到选中项
- 可通过插槽自定义头部区域，如搜索

### 省份选择弹窗 (ProvincePopup)
#### 功能
提供省份选择功能，默认包含"全国"选项

#### Props
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| show | boolean | 是 | false | 控制弹窗显示状态 |
| selected | RegionItem | 否 | NATION_WIDE_REGION | 已选省份信息 |

#### 使用场景
适用于需要用户选择省份的场景，如地区筛选、地域统计等

#### 示例代码
```vue
<template>
  <ProvincePopup v-model:show="showProvince" v-model:selected="selectedProvince" />
</template>

<script setup>
import ProvincePopup from 'shared/helper-components/region-popup/province-popup.vue'
import { ref } from 'vue'

const showProvince = ref(false)
const selectedProvince = ref()
</script>
```

#### 注意事项
- 默认选中"全国"，可通过v-model:selected绑定选中的省份
- 依赖useRegionInfo组合式API获取省份数据

### 城市选择弹窗 (CityPopup)
#### 功能
提供城市选择功能，支持通过侧边栏切换省份

#### Props
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| show | boolean | 是 | false | 控制弹窗显示状态 |
| selected | RegionItem | 是 | - | 已选城市信息 |

#### 使用场景
适用于需要精确到城市级别的地区选择

#### 示例代码
```vue
<template>
  <CityPopup v-model:show="showCity" v-model:selected="selectedCity" />
</template>

<script setup>
import CityPopup from 'shared/helper-components/region-popup/city-popup.vue'
import { ref } from 'vue'

const showCity = ref(false)
const selectedCity = ref()
</script>
```

#### 注意事项
- 自动根据选中的省份加载对应的城市列表
- 支持通过侧边栏快速切换省份

### 地区筛选器 (RegionFilter)
#### 功能
显示当前选中地区并提供地区切换功能

#### Props
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| defaultSelected | RegionItem | 是 | - | 默认选中地区 |
| tips | string | 否 | - | 提示文案 |

#### 使用场景
适用于需要展示和切换地区的筛选场景，（全国｜省份｜地区）

#### 示例代码
```vue
<template>
  <RegionFilter 
    :defaultSelected="defaultRegion" 
    tips="每周仅可切换一次战区"
  />
</template>

<script setup>
import RegionFilter from 'shared/helper-components/region-popup/region-filter.vue'
import { ref } from 'vue'

const defaultRegion = ref({
  code: 1,
  fullName: '全国',
  shortName: '全国'
})
</script>
```

#### 注意事项
- 自动根据选中的地区生成地区层级（全国>省份>城市）
- 内置提示信息展示功能

## 组合式API

### useAudio
管理全局音频状态

### useGameState
获取和管理游戏运行状态

### useSidebar
处理WeGame客户端左侧游戏列表的状态（收起/展开/不显示）

## 工具函数

### 营销广告
```javascript
import { initMarketingFloater } from 'shared/helper-components/utils/marketing-floater';

// 初始化营销浮动广告
initMarketingFloater(GAME_ID);
```

### 页面跳转
```javascript
import { jumpPage } from 'shared/helper-components/utils/jump';

// 系统浏览器打开
jumpPage({
    url: 'https://www.wegame.com.cn'
});

// WeGame浏览器打开
jumpPage({
    url: 'https://www.wegame.com.cn',
    jump_url_type: 'new_wegame_window'
});

// 跳转WeGame商店
jumpPage({
    url: 'https://www.wegame.com.cn/store/1/',
    jump_url_type: 'direct',
    jump_appid: 50000013
});
```

### Feeds SDK
```javascript
import { loadFeedsSdk } from 'shared/helper-components/utils/load-feeds-sdk';

// 加载Feeds SDK并调用
await loadFeedsSdk();
window.Feeds.popup({
    feedsId: feed.iid
});
```

### WeGame版本适配
```javascript
import { isV3, adaptiveWeGameVersionUI } from 'shared/helper-components/utils/wg';

// 检查是否为WeGame 3.0
isV3();

// 适配WeGame版本UI
adaptiveWeGameVersionUI();
```

## 开发指南
### 本地开发
```bash
// 在引用项目里开发
npm install
npm run dev
```
### OSS配置
#### OSS地址
[test鸣潮](http://test.oss.s.tgp.oa.com/assets/index.html#/main/template/data/list/1471)

[qa&现网鸣潮](http://oss.s.tgp.oa.com/assets/index.html#/main/template/data/list/1338)

#### 操作步骤
1. 现在test环境OSS的“定制游戏助手”创建子分类，名称为游戏中文名（例如鸣潮）
2. 先运行oss-api，然后再执行所需的oss（例如轮播图oss-carousel，修改对应的GAME_ID跟GAME_NAME）
3. 把OSS表名加到数据隔离[test](http://test.oss.s.tgp.oa.com/assets/index.html#/main/template/data/list/1189)、[qa](http://oss.s.tgp.oa.com/assets/index.html#/main/template/data/list/1006)

#### 注意事项
只在test环境运行（功能测试没问题再同步到现网）

现网修改不发布则QA，发布则现网

常用类型参考oss-type.js

### 代码规范
- 使用ESLint进行代码检查
- 组件命名采用PascalCase
- 遵循Vue官方风格指南

## 数据上报
[数据上报规范定义](https://tapd.woa.com/TGC/markdown_wikis/show/#1210041181002542237)

## 相关资料
- [WgUI组件库文档](https://wgui.pages.woa.com/doc/guide/getting-started.html)
- [通用配置业务字段定义](https://iwiki.woa.com/p/4008610980)
