.helper-news {
  --news-tab-color-normal: rgba(255, 255, 255, 0.6);
  --news-tab-color-hover: rgba(255, 255, 255, 1);
  --news-tab-color-current: var(--helper-color-brand1);
  --news-title-color-normal: rgba(255, 255, 255, 0.6);
  --news-title-color-hover: rgba(255, 255, 255, 1);
  --news-time-color-normal: rgba(255, 255, 255, 0.4);
}
.news-header {
  position: relative;
  display: flex;
  align-items: center;
}
.news-tab {
  width: 100%;
  flex: 1;
  display: flex;
  position: relative;
}
.tab-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  font-size: 0.14rem;
  color: var(--news-tab-color-normal);
  font-weight: 500;
  transition: color 0.2s linear;
  &:hover {
    color: var(--news-tab-color-hover);
  }
  &.current,
  &.current:hover {
    color: var(--news-tab-color-current);
  }
}

.news-tools {
  color: var(--news-tab-color-normal);
  font-weight: 500;
  transition: color 0.2s linear;
  &:hover {
    color: var(--news-tab-color-hover);
  }
}

.news-more {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.news-list {
  .helper-nodata {
    padding: 0.25rem 0;
  }
}
.news-item {
  display: flex;
  align-items: center;
  position: relative;
  font-size: 0.12rem;
  cursor: pointer;
}
.news-title {
  width: 100%;
  flex: 1;
  overflow: hidden;
  color: var(--news-title-color-normal);
  cursor: pointer;
  transition: color 0.2s linear;
  &:hover {
    color: var(--news-title-color-hover);
  }
}
.news-title-text {
  display: inline-block;
  max-width: calc(100% - 0.2rem);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.news-time {
  font-size: 0.12rem;
  color: var(--news-time-color-normal);
}
