import { baseRequest } from '@tencent/wegame-web-sdk';
import { InfoItem } from './interface';

export const getInfoData = async ({ gameId }: { gameId: string }) => {
  try {
    const resp = await baseRequest<{
      items: { tab: string; tab_index: number; items: string[] }[];
    }>({
      url: `/api/rail/web/data_filter/game_config/condition_search`,
      data: {
        data_names: 'helper_components_info',
        search_pair: [{ key: 'game_id', value: gameId }],
        stamp: {}
      },
      method: 'POST'
    });

    const list = (resp?.items || [])
      .sort((a, b) => Number(a.tab_index) - Number(b.tab_index))
      .map(item => {
        const items = item.items
          .map(item => {
            try {
              return JSON.parse(item) as InfoItem;
            } catch (error) {
              console.error('parse info item failed:', error);
            }
            return null;
          })
          .filter(Boolean) as InfoItem[];
        return { name: item.tab, list: items };
      });

    return { more: { jump_url: '' }, tabs: list };
  } catch (error) {
    console.error('fetch helper_components_info failed:', error);
  }

  return { more: { jump_url: '' }, tabs: [] };
};
