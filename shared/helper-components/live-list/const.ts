export const LIVE_PLATFORMS = [
  { id: 1, key: 'panda', title: '熊猫', class: 'panda' },
  { id: 2, key: 'longzhu', title: '龙珠', class: 'lz' },
  { id: 4, key: 'douyu', title: '斗鱼', class: 'douyu' },
  { id: 5, key: 'huomao', title: '火猫', class: 'huomao' },
  { id: 6, key: 'qtSelf', title: '自建', class: '' },
  { id: 7, key: 'quanminTV', title: '全民', class: 'qm' },
  { id: 8, key: 'huya', title: '虎牙', class: 'huya' },
  { id: 9, key: 'zhanqi', title: '战旗', class: 'zhanqi' },
  { id: 10, key: 'qiESports', title: '企鹅电竞', class: 'egame' },
  { id: 11, key: 'yy', title: 'YY', class: 'yy' },
  { id: 12, key: 'chushou<PERSON>', title: '触手', class: '' }
];

export type LivePlatformKey = keyof typeof LIVE_PLATFORMS;

export const getLivePlatform = (liveType: number) => {
  return LIVE_PLATFORMS.find(item => item.id === liveType) || null;
};
