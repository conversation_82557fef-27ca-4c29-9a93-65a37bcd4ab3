@charset "utf-8";

:root {
  --fill-popbox-header-bg:#303033;
}

.popbox-agetips.theme-cn-dark {
  --color-text-5:#FFF;
  --color-text-2: rgba(255,255,255,0.6);
  --color-fill-1: #4c4c50;
}

.wg-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 200;
  background-color: rgba(0, 0, 0, 0.5);
}

.wg-popbox-wrap {
  overflow: auto;
  overflow-x: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.wg-popbox {
  display: block;
  min-width: 480px;
  overflow: hidden;
  background-color: var(--fill-popbox-header-bg);
}

.wg-popbox-hd {
  position: relative;
  padding: 20px 40px 0 40px;
  font-size: 0;
  white-space: nowrap;
  word-wrap: normal;
  text-align: left;
}

.wg-popbox-title {
  display: block;
  color: #3c3c3c;
  color: var(--color-text-5);
  line-height: 40px;
  font-weight: 400;
  font-size: 20px;
}

.wg-popbox-close {
  position: absolute;
  right: 5px;
  top: 50%;
  width: 24px;
  height: 24px;
  margin-top: -12px;
  font-size: 24px;
  line-height: 24px;
  color: #3c3c3c;
  color: #3c3c3c;
  color: var(--color-text-1);
  opacity: 0.8;
  text-align: center;
  cursor: pointer;
}

.wg-popbox-close:hover {
  opacity: 1;
}

.wg-popbox-bd {
  line-height: 1.5;
  overflow: hidden;
  overflow-y: auto;
  background-image: url("data:image/png;base64,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");
  background-position: 100% 90%;
  background-repeat: no-repeat;
  background-size: 350px auto;
}

.wg-popbox-ft {
  padding: 20px 40px 20px 40px;
}

.wg-popbox-ft-l {
  position: absolute;
  left: 0;
  top: 0;
  right: 220px;
  height: 100%;
  padding: 12px;
  font-size: 0;
  white-space: nowrap;
  word-wrap: normal;
}

.age-tips {
  position: absolute;
  left: 40px;
  bottom: 80px;
  z-index: 50;
  padding: 6px 2px 4px 2px;
  background-color: #fff;
  background-color: var(--color-fill-1);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  -webkit-box-shadow: 0 0 10px 0px rgba(0, 0, 0, 0.15);
          box-shadow: 0 0 10px 0px rgba(0, 0, 0, 0.15);
  -webkit-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

.age-tips:hover .age-tips-icon,
.age-tips:hover .age-tips-txt {
  opacity: 1;
}

.age-tips-icon {
  width: 41px;
  height: 45px;
  background-image: url("data:image/png;base64,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");
  background-repeat: no-repeat;
  display: block;
  margin: 0 auto;
  opacity: 0.65;
  -webkit-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

.age-tips-icon--12 {
  background-image: url("data:image/png;base64,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");
  background-repeat: no-repeat;
}

.age-tips-icon--16 {
  background-image: url("data:image/png;base64,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");
  background-repeat: no-repeat;
}

.age-tips-txt {
  text-align: center;
  font-size: 12px;
  color: #3c3c3c;
  color: var(--color-text-5);
  font-weight: bold;
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  display: block;
  opacity: 0.65;
  -webkit-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

.age-tips {
  display: none;
}

@media all and (min-width: 1150px) {
  .age-tips {
    display: block;
  }
}

.age-popbox-cont {
  margin: 20px 40px;
  line-height: 1.65;
}

.age-popbox-cont .tip-item {
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 0px;
  color: #757575;
  color: var(--color-text-2);
  margin-top: 20px;
  text-align: justify;
}

.age-popbox-cont .tip-list {
  font-size: 14px;
  margin-left: 45px;
  color: #757575;
  color: var(--color-text-2);
  line-height: 21px;
}

.age-popbox-cont .tip-list .tip-list-item {
  margin-top: 13px;
  text-align: justify;
}

.age-popbox-cont div, .age-popbox-cont p, .age-popbox-cont ul, .age-popbox-cont li, .age-popbox-cont span, .age-popbox-cont strong, .age-popbox-cont dl, .age-popbox-cont dt, .age-popbox-cont dd, .age-popbox-cont ol, .age-popbox-cont table, .age-popbox-cont td, .age-popbox-cont th, .age-popbox-cont thead, .age-popbox-cont tbody, .age-popbox-cont tfoot, .age-popbox-cont strong, .age-popbox-cont em, .age-popbox-cont b, .age-popbox-cont i {
  font-size: 14px !important;
  color: #757575 !important;
  color: var(--color-text-2) !important;
  background: transparent !important;
}

.age-popbox-cont ul,
.age-popbox-cont li,
.age-popbox-cont p {
  padding-bottom: 10px;
}

.age-popbox-cont ul:last-child,
.age-popbox-cont li:last-child,
.age-popbox-cont p:last-child {
  padding-bottom: 0;
}

.age-popbox-cont ul,
.age-popbox-cont li {
  list-style: disc;
}

.age-popbox-cont ul {
  margin-left: 20px;
}

.age-popbox-cont img {
  max-width: 100% !important;
}

.wg-popbox {
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 271px 157px;
  background-position: bottom 13px right;
  position: relative;
  -webkit-box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.16);
          box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.16);
  display: block;
  color: #3c3c3c;
  color: var(--color-text-1);
  width: 680px;
  /* 弹窗内容 */
  /* 弹窗底部 */
}

.wg-popbox-close {
  height: 24px;
  width: 24px;
  position: absolute;
  right: 8px;
  top: 20px;
  z-index: 3;
  opacity: 0.8;
  cursor: pointer;
}

.wg-popbox-close:hover {
  opacity: 1;
}

.wg-popbox-close::before, .wg-popbox-close::after {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  content: "";
  height: 12px;
  width: 2px;
  background-color: #999999;
}

.wg-popbox-close::before {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}

.wg-popbox-close::after {
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}

.wg-popbox-bd {
  overflow: hidden;
  overflow-y: scroll;
  font-size: 14px;
  height: 400px;
}

.wg-popbox-bd:nth-child(1) {
  padding-top: 20px;
}

.wg-popbox-ft {
  position: relative;
  padding-bottom: 26px;
}

.wg-popbox-ft-l {
  float: left;
}

.wg-popbox-ft-r {
  text-align: left;
}

.wg-popbox-minSize {
  min-width: 480px;
  min-height: 234px;
}

.wg-popbox .wg-button {
  min-width: 86px;
  height: 26px;
  line-height: 26px;
  border-radius: 15px;
  padding: 0 24px !important;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-weight: bold;
  display: inline-block;
  text-align: center;
  font-size: 14px;
  text-decoration: none;
  -webkit-transition: background-color 0.15s linear;
  transition: background-color 0.15s linear;
}

.wg-popbox .wg-button--primary {
  margin-right: 20px;
  background-color: #ffc107;
  color: #3c3c3c;
}

.wg-popbox .wg-button--primary:hover {
  background: #ffd54f;
}

.wg-popbox .wg-button--primary.disabled {
  background-color: #ffc107;
  opacity: 0.2;
}

.wg-popbox .wg-button--default {
  color: #3c3c3c;
  color: var(--color-text-1);
  background: rgba(255, 255, 255, 0.3);
}

.wg-popbox .wg-button--default:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.wg-popbox .wg-button--default.disabled {
  opacity: 0.2;
}

:root {
  --color-popbox-mask-fixed:#000;
}

html:not(.is-transitional) .wg-mask {
  background: none !important;
}

html:not(.is-transitional) .wg-mask::before {
  display: block !important;
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-popbox-mask-fixed) !important;
  opacity: 0.5;
  pointer-events: none;
  z-index: -1;
}

html:not(.is-transitional).theme-cn-dark .wg-mask:before {
  -webkit-filter: brightness(0.2);
          filter: brightness(0.2);
}

@media (prefers-color-scheme: dark) {
  html:not(.is-transitional) .wg-mask::before {
    -webkit-filter: brightness(0.2);
            filter: brightness(0.2);
  }
}

.age-tips > img {
  display: none;
}

.age-tips--images {
  background: none;
  border: none;
  padding: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.age-tips--images .age-tips-txt,
.age-tips--images .age-tips-icon {
  display: none;
}

.age-tips--images > img {
  display: block;
  width: 52px;
}