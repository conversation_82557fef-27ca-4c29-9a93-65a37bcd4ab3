<template>
  <div v-show="ageImage">
    <div class="age-tips age-tips--images" @click="showAgeTips(true)">
      <img :src="ageImage" alt="" />
    </div>

    <!-- 适龄弹框 S  -->
    <div v-if="isShowTips" class="wg-mask popbox-agetips theme-cn-dark">
      <div class="wg-popbox-wrap">
        <div class="wg-popbox">
          <div class="wg-popbox-hd">
            <!-- 弹框标题文案 S  -->
            <span class="wg-popbox-title">适龄提醒</span>
            <!-- 弹框标题文案 E  -->

            <!-- 弹框关闭按钮 S  -->
            <a class="wg-popbox-close" @click="showAgeTips(false)"></a>
            <!-- 弹框关闭按钮 E  -->
          </div>
          <div class="wg-popbox-bd">
            <!-- eslint-disable vue/no-v-html -->
            <div class="age-popbox-cont" v-html="ageTips"></div>
          </div>
          <div class="wg-popbox-ft">
            <div class="wg-popbox-ft-r">
              <a
                class="wg-button wg-button--primary"
                href="javascript:void(0);"
                @click="showAgeTips(false)"
              >
                <span>确定</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 适龄弹框 E  -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { baseRequest } from '@tencent/wegame-web-sdk';

const props = defineProps<{
  gameId: string;
}>();

const isShowTips = ref(false);
const ageImage = ref('');
const ageTips = ref('');

function showAgeTips(b: boolean) {
  isShowTips.value = b;
}

async function fetchOssData() {
  if (!props.gameId) return;
  try {
    const resp = await baseRequest({
      url: `/api/rail/web/data_filter/game_config/condition_search`,
      data: {
        data_names: 'helper_age_tips',
        search_pair: [{ key: 'game_id', value: props.gameId }],
        stamp: {}
      },
      method: 'POST'
    });
    const data = resp?.items?.[0];
    if (data) {
      ageImage.value = data.cadpa_img || '';
      ageTips.value = data.age_tips || '';
    }
  } catch (error) {
    console.error(error);
  }
}

onMounted(() => {
  fetchOssData();
});
</script>
<style lang="scss">
@use './assets/scss/age-tips-popbox.scss';
</style>
