<template>
  <Teleport to="body">
    <div class="video-preview" :class="videoPlayerOpt?.parentClass">
      <div class="video-preview-modalbox">
        <div class="video-preview-modal">
          <!-- 关闭按钮 S  -->
          <span class="video-preview-close" @click="clickClose">
            <i class="video-preview-close-icon"></i>
          </span>
          <!-- 关闭按钮 E  -->

          <div class="video-preview-cont">
            <div class="video-preview-container">
              <!-- 视频播放区域 S  -->
              <VideoPlayer
                v-if="url || vid"
                :dom-id="`one-video-player-${uuid}`"
                :url="url"
                :vid="vid"
                :play="true"
                :custom-class="videoPlayerOpt?.customClass"
                :off-screen-pause="videoPlayerOpt?.offScreenPause"
                :config="videoPlayerOpt?.config"
                :volume="videoPlayerOpt?.volume"
              />
              <!-- 视频播放区域 E  -->
            </div>
          </div>
        </div>
      </div>
      <div class="video-preview-mask"></div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
import { ISuperPlayerConfig } from '@tencent/super-player';

import { generateUUID } from '../utils/uuid';
import VideoPlayer from '../video-player';

const emits = defineEmits<(e: 'close') => void>();

withDefaults(
  defineProps<{
    appId: string;
    url?: string;
    vid?: string;
    videoPlayerOpt?: {
      parentClass?: string | string[];
      customClass?: string | string[];
      offScreenPause?: boolean;
      config?: Omit<ISuperPlayerConfig, 'container'>;
      volume?: number;
    };
  }>(),
  {
    url: '',
    vid: '',
    slideWidth: 680,
    videoPlayerOpt: () => {
      return {};
    }
  }
);

const uuid = generateUUID();

const clickClose = () => {
  emits('close');
};
</script>
<style lang="scss">
@use './assets/scss/video-preview.scss';
</style>
