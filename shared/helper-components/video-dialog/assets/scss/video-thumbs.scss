
.video-slider-list{
  overflow: hidden;
}
.video-slider-inner{
  position: relative;
  white-space: nowrap;
}
.video-slider-item{
  display: inline-block;
  padding: 0 20px 0 0;
}
.video-thumb-card{
  position: relative;
  display: inline-block;
  opacity: 0.5;
  cursor: pointer;
}
.video-thumb-card-cover{
  & > img{
    width: 200px;
    height: 120px;
    object-fit: cover;
  }
}
.video-thumb-card-info{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  background: rgba($color: #000000, $alpha: 0.5);
  backdrop-filter: blur(10px);
}
.video-thumb-card-title{
  color: #FFF;
  font-size: 12px;
  font-weight: 500;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 5px 5px;
}

.video-slider-button{
  width: 32px;
  height: 32px;
  position: absolute;
  top: 50%;
  margin-top: -16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.8;
  &:hover{
    opacity: 1;
  }
  
  &.disabled{
    opacity: 0.3;
    pointer-events: none;
    cursor: default;
  }
  &::after{
    content: "";
    width: 16px;
    height: 16px;
    border: 3px solid rgba($color: #000000, $alpha: 0.5);
    display: block;
    border-radius: 2px;
    border-top:none;
    border-right:none;
    transform: rotate(45deg);
  }
}
.slider-button-prev{
  left: 8px;
}
.slider-button-next{
  right: 8px;
  &::after{
    content: "";
    transform: rotate(-135deg);
  }
}

.video-slider-item{
  &:hover{
    .video-thumb-card{
      opacity: 1;
    }
  }
  &.current{
    .video-thumb-card{
      opacity: 1;
    }
  }
}


