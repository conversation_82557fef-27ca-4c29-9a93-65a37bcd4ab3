<template>
  <div
    v-if="kvData && visible"
    :class="[
      'big-suit',
      {
        'big-suit-cursor': kvData.link && !isLinkSelf,
        'big-suit-noleading': !isSplashScreen
      }
    ]"
    @click="clickOpenPage"
  >
    <div class="big-suit-main">
      <HelperImage
        v-if="kvData.bg || kvData.poster"
        class="big-suit-bg-pic-bg"
        :src="kvData.bg || kvData.poster"
      />
      <div v-if="kvData.video" class="leading-video">
        <!-- eslint-disable-next-line @tencent/wegame/vue-a11y-media-has-caption -->
        <video ref="video" :muted="!!kvData.audio || muted" autoplay loop>
          <source :src="kvData.video" />
        </video>
      </div>
      <audio
        v-show="false"
        v-if="kvData.audio"
        ref="audio"
        loop
        muted
        :src="kvData.audio"
        style="position: absolute; top: 0; left: 0; opacity: 0"
      ></audio>
      <div class="big-suit-row-box" @click.stop="goMainBlock">
        <div class="big-suit-row-content">
          <span class="big-suit-row-text">{{ entryText }}</span>
          <i class="big-suit-row"></i>
        </div>
      </div>
      <div
        :class="['suit-kv-musicbtn', { 'suit-kv-musicbtn--pause': muted }]"
        @click.stop="toggleMuted"
      ></div>
      <div class="big-suit-showtitle">
        <div class="big-suit-showtitle-logo"></div>
        <p v-if="kvData.title" class="big-suit-showtitle-text">
          {{ kvData.title }}
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  watch,
  defineEmits,
  watchEffect,
  onBeforeUnmount,
  onMounted,
  computed
} from 'vue';
import { useMounted } from '@vueuse/core';
import HelperImage from '../helper-image/index.vue';
import { useKv } from '../composable/use-kv';
import { KvStatus } from './kv-status';
import { reportEvent } from '../utils/report';

const emits = defineEmits<(e: 'go-main') => void>();

const props = withDefaults(
  defineProps<{
    appId: string;
    entryText?: string;
  }>(),
  {
    entryText: '前往首页'
  }
);

const isMounted = useMounted();
const { kvData, kvStatus, isLinkSelf, goMain, openLink } = useKv(props.appId);
const visible = computed(() => {
  return (
    isMounted.value &&
    ![KvStatus.pre, KvStatus.none, KvStatus.hide].includes(kvStatus.value)
  );
});

const video = ref<HTMLVideoElement>();
const audio = ref<HTMLAudioElement>();

const muted = ref(true);

const isSplashScreen = ref(true);
let splashScreenTimer: ReturnType<typeof setTimeout>;

const toggleMuted = () => {
  muted.value = !muted.value;
  reportEvent({
    appId: props.appId,
    block: 'helper_kv',
    action: 'click_mute',
    ext: muted.value ? '1' : '0'
  });
};

const clickOpenPage = () => {
  openLink();
  reportEvent({
    appId: props.appId,
    block: 'helper_kv',
    action: 'open_page',
    ext: muted.value ? '1' : '0'
  });
};

watchEffect(() => {
  if (!audio.value) {
    return;
  }

  if (muted.value || kvStatus.value === KvStatus.fold) {
    audio.value.muted = true;
    audio.value.pause();
    audio.value.currentTime = 0;
  } else {
    audio.value.muted = false;
    audio.value.play();
  }
});

const goMainBlock = () => {
  goMain();
  emits('go-main');
  reportEvent({
    appId: props.appId,
    block: 'helper_kv',
    action: 'go_main'
  });
};

onMounted(() => {
  watch(
    kvStatus,
    (val) => {
      if (val === KvStatus.unfold) {
        isSplashScreen.value = true;
        clearTimeout(splashScreenTimer);
        splashScreenTimer = setTimeout(() => {
          isSplashScreen.value = false;
        }, 10);

        if (video.value) {
          video.value.currentTime = 0;
          video.value.play();
        }

        // 添加自定义色值
        const customStyle = `--big-suit-theme-color: ${kvData.value?.themeColor};`;
        document.body.setAttribute('style', customStyle);
      } else if (val === KvStatus.fold) {
        if (video.value) {
          video.value.pause();
        }
        document.body.removeAttribute('style');
      }
    },
    {
      immediate: true
    }
  );
});

onBeforeUnmount(() => {
  clearTimeout(splashScreenTimer);
});
</script>
<style lang="scss">
@use './index.scss';
</style>
