.big-suit {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 500;

  img,
  video,
  source {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .leading-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(
        52.94% 52.94% at 62.78% 47.06%,
        rgba(0, 0, 0, 0) 38.02%,
        rgba(0, 0, 0, 0.6) 100%
      );
      z-index: 1;
      pointer-events: none;
    }
  }
}

.big-suit-cursor {
  cursor: pointer;
}

.big-suit-main {
  width: 100%;
  height: 100%;
}

.big-suit-bg-pic-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      52.94% 52.94% at 62.78% 47.06%,
      rgba(0, 0, 0, 0) 38.02%,
      rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 1;
    pointer-events: none;
  }
}

.big-suit-row-text,
.suit-kv-musicbtn,
.big-suit-showtitle {
  display: none;
}

.big-suit-row-box {
  cursor: pointer;
  position: fixed;
  left: 50%;
  transform: translate(-50%, 0);
  bottom: 120px;
  z-index: 20;
}

.helper-kv-entry {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  z-index: 490;
  text-align: center;
}
.helper-kv-entry-link {
  display: inline-block;
}
.helper-kv-entry-pic {
  max-height: 80px;
  width: auto;
  display: block;
  margin: 0 auto;
}

.helper-v3 .helper-kv-entry {
  top: 74px;
}
.big-suit-row,
.helper-kv-entry-row {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: pointer;
  animation: downTop 2s linear infinite;
  transition: opacity 0.3s ease-in-out;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoBAMAAAB+0KVeAAAAIVBMVEUAAADj4+Pj4+Pf39/j4+Pi4uLk5OTj4+Pj4+Pn5+fj4+M9/BacAAAACnRSTlMAf4AQz6Dfv5AgAtyEvwAAAGRJREFUKM/VzcENgCAQRFGMISonSrAIYhGWQAmWYAl2IUeqZLiyvwHm+PM26+ZaqCfFQlHURqBLJfoR9Ugz0Y3oehNNSB+gh+I/xkvn0TwCmAC6F6AnmAHuBEOHECNEQRMF51sDHKYvde7CnTUAAAAASUVORK5CYII=');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    transform: rotate(-90deg);
  }

  &::after {
    content: '';
    position: absolute;
    inset: -10px;
  }

  &:hover {
    opacity: 0.8;
  }
}

.helper-kv-entry-row {
  &::before {
    transform: rotate(90deg);
  }
}

// .big-suit-row {
//   &::before {
//     transform: rotate(90deg);
//   }
// }

@keyframes downTop {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(10px);
  }

  100% {
    transform: translateY(0);
  }
}

.slidedown {
  .big-suit {
    animation: kvSlideUp 0.3s ease-in-out 1 both;
    pointer-events: none;
  }
}

.slideup {
  .big-suit {
    animation: kvSlideDown 0.3s ease-in-out 1 both;
  }
}
.default-fold {
  .big-suit {
    transform: translateY(-100%);
    opacity: 0;
    pointer-events: none;
    animation: none;
  }
}
@keyframes kvSlideDown {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }

  100% {
    transform: none;
    opacity: 1;
  }
}

@keyframes kvSlideUp {
  0% {
    transform: translateY(0);
    opacity: 1;
  }

  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}
