.audio-ctrl-wrapper {
  display: flex;
  align-items: center;
}

.audio-ctrl-icon {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  cursor: pointer;
  position: relative;
  &::after,
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    transition: opacity 0.3s;
  }

  &::before {
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABFSURBVHgB7c6hEcAwDARBOX2o/8JUSMID8mMUg1348+CqAP61ds4zc7+37l67ny9XHUZQIigRlAhKBCWCEkGJoEQQwOkefU4IKG/jVFsAAAAASUVORK5CYII=') center center no-repeat;
    background-size: contain;
    opacity: 0;
  }
  &::after{
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD7SURBVHgB7ZbhDYIwEIWv4hoQGMENcANXcAOdQNxAJ3EEcZISYAAHgNRrIsaYkNi7a/THfUlDIDT58vKOAqAoivJbTMjLfd9b51z+vD2maVqBMEFCXde5j0f1MAzboigaEGIBPMokSa7W2hyE4AqBMSb3UpheCQKwhTxeCi9eqgImIkJvHLhS3FLPQS67dEIT5LLHEnqVvW3bTci+aEIeL4XrEjKBUYUoxBa64yrxiKm/3bCESOCZ14zjuA6dtFgJ1Sizooy9eEKYzCnLsj0QkRbaocwZGEgJ+fJusLw3YMIWopZ3Dm6pyeWdIygh/Oo20y8st7yKoij/ygP3QWKSDZI0CgAAAABJRU5ErkJggg==') center center no-repeat;
    background-size: contain;
  }
}  
.audio-ctrl-icon--play {
  &::after {
    opacity: 0 !important;
  }

  &::before {
    opacity: 1 !important;
  }
}

.audio-ctrl-infobox {
  flex: 1;
  width: 100%;
  margin-left: 10px;
}

.audio-ctrl-info {
  display: flex;
  justify-content: space-between;
}

.audio-ctrl-err {
  color: #c22d2c;
}

.audio-progress {
  width: 100%;
  position: relative;
  display: inline-flex;
  align-items: center;
  // justify-content: center;
}

.audio-progress-line {
  width: 100%;
  height: 6px;
  background-color: rgba(255,255,255,0.2);
  cursor: pointer;
}
.audio-progress-bg {
      height: 100%;
      background: rgba(255,255,255,0.4);
    }

.audio-progress-icon {
  width: 10px;
  height: 10px;
  background-color: rgba(255,255,255,0.8);
  cursor: pointer;
  position: absolute;
  
}