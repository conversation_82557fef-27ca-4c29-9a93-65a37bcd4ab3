.audio-list {
  flex: 1;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.audio-item {
  margin-bottom: 10px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
  box-sizing: border-box;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

.audio-item-cover {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  margin-right: 12px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.audio-item-id {
  width: 40px;
  text-align: center;
}

.audio-item-info {
  flex: 1;
  width: 100%;
}

.audio-item-state {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

  .audio-item--cur {

    .audio-item-time {
      opacity: 0;
    }

    .audio-item-icon {
      opacity: 1;
    }
  }

.audio-item-icon {
  position: absolute;
  margin-right: 8px;
  width: 32px;
  height: 32px;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD7SURBVHgB7ZbhDYIwEIWv4hoQGMENcANXcAOdQNxAJ3EEcZISYAAHgNRrIsaYkNi7a/THfUlDIDT58vKOAqAoivJbTMjLfd9b51z+vD2maVqBMEFCXde5j0f1MAzboigaEGIBPMokSa7W2hyE4AqBMSb3UpheCQKwhTxeCi9eqgImIkJvHLhS3FLPQS67dEIT5LLHEnqVvW3bTci+aEIeL4XrEjKBUYUoxBa64yrxiKm/3bCESOCZ14zjuA6dtFgJ1Sizooy9eEKYzCnLsj0QkRbaocwZGEgJ+fJusLw3YMIWopZ3Dm6pyeWdIygh/Oo20y8st7yKoij/ygP3QWKSDZI0CgAAAABJRU5ErkJggg==') center center no-repeat;
    background-size: contain;
  opacity: 0;
}

.audio-item--play {
  .audio-item-icon {
    opacity: 0 !important;
  }

  &:hover {
    .audio-item-icon {
      opacity: 0 !important;
    }
  }
  .audio-playing-icon {
    opacity: 1 !important;
  }
}


.audio-playing-icon {
  margin-right: 8px;
  height: 16px;
  position: absolute;
  box-sizing: border-box;
  display: flex;
  align-items: flex-end;
  column-gap: 3px;
  justify-content: space-between;
  opacity: 0;
  transition: opacity 0.3s;
}

.audio-playing-bar {
  width: 3px;
  animation: voice 0ms -800ms linear infinite alternate;
  background: #fff;
  height: 90%;
}

@keyframes voice {
  0% {
    height: 0;
  }

  100% {
    height: 90%;
  }
}

.audio-playing-bar:nth-child(1) {
  animation-duration: 0.4s;
}

.audio-playing-bar:nth-child(2) {
  animation-duration: 0.7s;
}

.audio-playing-bar:nth-child(3) {
  animation-duration: 0.9s;
}

.audio-playing-bar:nth-child(4) {
  animation-duration: 0.6s;
}