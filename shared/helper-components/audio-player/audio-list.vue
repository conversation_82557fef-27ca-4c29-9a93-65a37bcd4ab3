<template>
  <div class="audio-list">
    <div
      v-for="(item, index) in audioList"
      :key="index"
      ref="audioRefs"
      :class="[
        'audio-item',
        'audio-item--' + index,
        {
          'audio-item--cur': item.url === audio?.url,
          'audio-item--play': item.url === audio?.url && isPlaying
        }
      ]"
      @click="clickPlay(item)"
    >
      <div v-if="item.albumCover" class="audio-item-cover">
        <img :src="item.albumCover" alt="" />
      </div>
      <div v-else class="audio-item-id">
        {{index + 1}}
      </div>
      <div class="audio-item-info">
        <div class="audio-item-title">
          {{ audioIndexVisible ? index + 1 + '.' : '' }}{{ item.title }}
        </div>
        <div class="audio-item-text">{{ item.artist }}</div>
      </div>
      <div class="audio-item-state">
        <div class="audio-item-time">{{ item.duration }}</div>
        <div class="audio-item-icon"></div>
        <div class="audio-playing-icon">
          <div class="audio-playing-bar"></div>
          <div class="audio-playing-bar"></div>
          <div class="audio-playing-bar"></div>
          <div class="audio-playing-bar"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue';
import { AudioItem } from './interface';
import { reportEvent } from '../utils/report';

const emits = defineEmits<{
  (e: 'play', value: { audio: AudioItem }): void;
  (e: 'update:globalMuted', value: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    globalMuted?: boolean;
    /**
     * 只用于上报
     */
    appId?: string;
    audio?: AudioItem | null;
    audioList: AudioItem[];
    isPlaying: boolean;
    /**
     * 是否显示第x首
     */
    audioIndexVisible?: boolean;
    /**
     * 是否把当前歌曲滚动可视区域
     */
    audioIntoView?: boolean;
    /**
     * 数据上报到ext字段
     */
    reportExt?: string;
  }>(),
  {
    globalMuted: false,
    appId: '',
    audio: null,
    isPlaying: false,
    audioIndexVisible: true,
    audioIntoView: true,
    reportExt: ''
  }
);

const audioRefs = ref<HTMLElement[]>([]);

const clickPlay = (audio: AudioItem) => {
  reportEvent({
    appId: props.appId,
    block: 'audio_list',
    action: 'play',
    title: audio.title,
    ext: props.reportExt
  });
  emits('update:globalMuted', false);
  emits('play', { audio });
};

const scrollIntoView = async () => {
  if (props.audioIntoView && audioRefs.value.length && props.audio) {
    // 等最新的audio-item--cur赋值生效
    await nextTick();
    const curItem = audioRefs.value.find(item =>
      item.classList.contains('audio-item--cur')
    );
    curItem?.scrollIntoView({ behavior: 'smooth' });
  }
};

watch(
  () => [props.audioIntoView, audioRefs.value.length, props.audio],
  async () => {
    scrollIntoView();
  },
  {
    immediate: true
  }
);
</script>

<style lang="scss">
@use './assets/scss/audio-list.scss';
</style>
