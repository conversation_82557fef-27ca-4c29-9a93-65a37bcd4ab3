import type { LyricMeta, LyricLine } from './interface';

export class LyricsParser {
  private lyrics: LyricLine[] = [];

  /**
   * 歌词元数据
   * ti 标题
   * ar 歌手
   * al 专辑名
   * by 作词人
   */
  private meta: LyricMeta = {
    ti: '',
    ar: '',
    al: '',
    by: ''
  };

  constructor(lrcContent: string) {
    this.parseLrc(lrcContent);
  }

  getMeta(): LyricMeta {
    return this.meta;
  }

  getLyrics(): LyricLine[] {
    return this.lyrics;
  }

  getCurrentLyric(currentTime: number): LyricLine | null {
    let currentLyric: LyricLine | null = null;
    for (let i = 0; i < this.lyrics.length - 1; i++) {
      if (
        currentTime >= this.lyrics[i].time &&
        currentTime < this.lyrics[i + 1].time
      ) {
        currentLyric = this.lyrics[i];
        break;
      }
    }
    return currentLyric;
  }

  private parseLrc(lrcContent: string): void {
    const lines = lrcContent.split('\n');
    const timeRegex = /\[(\d{2}):(\d{2})(?:\.(\d{2,3}))?]/;
    // 提取标题、歌手、专辑名和作词人
    const metaRegex = /\[(ti|ar|al|by):(.*?)\]/;

    // 重置清空
    this.meta = {
      ti: '',
      ar: '',
      al: '',
      by: ''
    };
    lines.forEach(line => {
      const match = line.match(timeRegex);
      if (match) {
        const minutes = parseInt(match[1], 10);
        const seconds = parseInt(match[2], 10);
        const milliseconds = parseInt(match[3], 10)
          ? parseInt(match[3], 10)
          : 0;
        const time = minutes * 60 + seconds + milliseconds / 1000;
        const text = line.replace(timeRegex, '').trim();

        this.lyrics.push({ time, text });
      } else {
        const metaMatch = line.match(metaRegex);
        if (metaMatch?.[1]) {
          const [, key, value] = metaMatch;
          this.meta[key as 'ti' | 'ar' | 'al' | 'by'] = value;
        }
      }
    });
  }
}
