<template>
  <div ref="container" class="audio-lyric">
    <div v-if="curMeta.ti" class="audio-lyric-ti">
      {{ curMeta.ti }}
    </div>
    <div v-if="curMeta.ar" class="audio-lyric-ar">{{ curMeta.ar }}</div>
    <div v-if="curMeta.al" class="audio-lyric-al">{{ curMeta.al }}</div>
    <div
      v-if="curMeta.ti || curMeta.ar || curMeta.al"
      class="audio-lyric-division"
    ></div>
    <p
      v-for="item in curLyrics"
      :key="item.time"
      ref="lineRefs"
      :class="{ cur: item.time === curLyric?.time }"
    >
      {{ item.text }}
    </p>
    <p v-if="curLyrics.length === 0" class="audio-lyric-no">纯音乐无歌词</p>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch, watchEffect } from 'vue';
import { useElementVisibility } from '@vueuse/core';
import { LyricsParser } from './lyrics-parser';
import { LyricMeta, LyricLine } from './interface';

const container = ref<HTMLElement | null>(null);
const lineRefs = ref<HTMLElement[]>([]);
const props = withDefaults(
  defineProps<{
    url?: string;
    currentTime?: number;
  }>(),
  {
    url: '',
    currentTime: 0
  }
);

const containerIsVisible = useElementVisibility(container);

const defaultMeta = { ti: '', ar: '', al: '', by: '' };

const curMeta = ref<LyricMeta>({ ...defaultMeta });
const curLyrics = ref<LyricLine[]>([]);

const curLyric = ref<LyricLine | null>(null);

const cacheData: Record<string, { meta: LyricMeta; lyrics: LyricLine[] }> = {};

watch(
  () => props.url,
  async val => {
    if (!val) {
      curMeta.value = { ...defaultMeta };
      curLyrics.value = [];
      return;
    }
    try {
      if (cacheData[val]?.lyrics.length) {
        curMeta.value = cacheData[val].meta;
        curLyrics.value = cacheData[val].lyrics;
      } else {
        const response = await fetch(val);
        if (!response.ok) {
          throw new Error(
            `Request failed with status code: ${response.status}`
          );
        }
        const lrcText = await response.text();
        const parser = new LyricsParser(lrcText);
        const meta = parser.getMeta();
        const lyrics = parser.getLyrics();
        curMeta.value = meta;
        curLyrics.value = lyrics;
        if (lyrics.length) {
          cacheData[val] = {
            meta,
            lyrics
          };
        }
      }
    } catch {
      curMeta.value = { ...defaultMeta };
      curLyrics.value = [];
    }
  },
  {
    immediate: true
  }
);

watch(
  () => props.currentTime,
  () => {
    const lyrics = curLyrics.value;
    if (!lyrics?.length) {
      curLyric.value = null;
      return;
    }
    for (let i = 0; i < lyrics.length; i++) {
      const line = lyrics[i];
      const startTime = Math.floor(line.time);
      const endTime =
        i < lyrics.length - 1 ? Math.floor(lyrics[i + 1].time) : Infinity;
      if (props.currentTime >= startTime && props.currentTime < endTime) {
        curLyric.value = line;
        break;
      }
    }
  }
);

const handleCurLyric = async () => {
  if (curLyric.value) {
    await nextTick();
    const curItem = lineRefs.value.find(item => item.classList.contains('cur'));
    curItem?.scrollIntoView({ block: 'center', behavior: 'smooth' });
  } else {
    container.value?.scrollTo({
      top: 0
    });
    return;
  }
};

watch(
  () => curLyric.value,
  () => {
    handleCurLyric();
  }
);

watchEffect(() => {
  // 部分歌词持续时间过久，因此需要在页面重新可见时初始化位置
  if (containerIsVisible.value) {
    handleCurLyric();
  }
});
</script>

<style lang="scss">
@use './assets/scss/lyric.scss';
</style>
