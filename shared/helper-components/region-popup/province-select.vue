<template>
  <div class="btn" @click="showArea = true">
    {{ selectedRegionInfo.shortName }}
  </div>
  <ProvincePopup
    v-model:show="showArea"
    v-model:selected="selectedRegionInfo"
  />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { type RegionItem, NATION_WIDE_REGION } from '../composable/use-region';
import ProvincePopup from './province-popup.vue';

const showArea = ref<boolean>(false);

/** 已选省份信息，默认全国 */
const selectedRegionInfo = defineModel<RegionItem>('selected', {
  default: NATION_WIDE_REGION
});
</script>
