<template>
  <AreaPopup
    v-model:selected="selectedRegionInfo"
    v-model:show="show"
    is-show-selected
    :sidebar-selected="sidebarSelected"
    :sidebar-region-list="provinceInfo"
    :region-list="cityList"
    @switch-sidebar="switchSidebar"
  />
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { type RegionItem, useRegionInfo } from '../composable/use-region';
import AreaPopup from './region-popup.vue';
import { whenever } from '@vueuse/core';

const { provinceInfo, getCityInfo, getProvinceName } = useRegionInfo();

/** 各省份城市列表 */
const cityList = ref<RegionItem[]>([]);
/** 已选城市信息 */
const selectedRegionInfo = defineModel<RegionItem>('selected', {
  required: true
});
/** 已选侧边栏信息，根据已选城市计算 */
const sidebarSelected = computed<RegionItem>(() =>
  getProvinceName(
    Number(`${String(selectedRegionInfo.value?.code ?? 0).slice(0, 2)}0000`)
  )
);
/** 显示状态 */
const show = defineModel<boolean>('show', {
  default: false,
  required: true
});

/** 切换省份时，更新城市列表 */
const switchSidebar = (item: RegionItem) => {
  cityList.value = getCityInfo(item.code);
  if (cityList.value.length === 0) {
    cityList.value = [item];
  }
};

// 初始化更新省份列表
whenever(
  () => provinceInfo.value,
  () => {
    cityList.value = getCityInfo(selectedRegionInfo.value?.code ?? 1);
  },
  { immediate: true }
);
</script>
