<template>
  <div class="btn" @click="showArea = true">
    {{ selectedRegionInfo?.fullName ?? '未知' }}
  </div>
  <CityPopup v-model:show="showArea" v-model:selected="selectedRegionInfo" />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { type RegionItem } from '../composable/use-region';
import CityPopup from './city-popup.vue';

const showArea = ref<boolean>(false);

/** 已选城市信息 */
const selectedRegionInfo = defineModel<RegionItem>('selected', {
  required: true
});
</script>
