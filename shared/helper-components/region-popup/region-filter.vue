<template>
  <div class="address-box">
    <div class="address-name">
      <WgTooltips
        v-for="region in regions"
        :key="region.code"
        :adaptive="region.code === 1"
        :text="region.code !== 1 ? transFullNameToShort(region.fullName) : ''"
      >
        <span
          :class="{ cur: curTab === region.code }"
          class="name"
          @click="selectRegion(region.code)"
          >{{ region.shortName }}榜</span
        >
      </WgTooltips>
    </div>
    <div class="address-icon" @click="showArea = true"></div>
    <CityPopup v-model:show="showArea" v-model:selected="currentSelected" />
    <div v-if="tips" class="local-tips">
      <div class="local-hover-tips">
        {{ tips }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import CityPopup from './city-popup.vue';
import { WgTooltips } from '@tencent/wg-ui';
import { NATION_WIDE_REGION, type RegionItem, useRegionInfo } from '../composable/use-region';
import './assets/scss/address.scss';
const { transFullNameToShort, getProvinceName, getProvince, regionCodeToName } =
  useRegionInfo();

const props = defineProps<{
  /** 默认选中地址 */
  defaultSelected: RegionItem;
  /** 提示文案 */
  tips?: string;
}>();

/** 当前选择信息 */
const currentSelected = ref<RegionItem>(props.defaultSelected);
/** 当前选中 tab ｜全国｜省份｜城市 */
const curTab = ref(props.defaultSelected.code);
/** 是否显示弹窗 */
const showArea = ref<boolean>(false);

const emits = defineEmits<(e: 'switch-region', value: number) => void>();

/** 切换地区触发事件 */
function selectRegion(code: number) {
  if (curTab.value !== code) {
    emits('switch-region', code);
    curTab.value = code;
  }
}

/** 地区弹窗变化上报 */
watch(
  () => currentSelected.value?.code,
  () => {
    selectRegion(currentSelected.value?.code);
  }
);

/** tab 信息根据当前选择地区变化 */
const regions = computed(() => {
  if (currentSelected.value?.code) {
    const provinceName = getProvinceName(currentSelected.value.code);
    const provinceCode = getProvince(currentSelected.value.code);
    const cityName = regionCodeToName(currentSelected.value.code);
    const area = cityName?.fullName?.split(',') || [];
    const country = NATION_WIDE_REGION;
    switch (area.length) {
      case 1: // 未知，只展示全国
        return [country];
      case 2: // 直辖市，没有城市
        return [
          country,
          {
            shortName: provinceName?.shortName,
            fullName: provinceName?.fullName,
            code: provinceCode
          }
        ];
      case 3: // 城市
        return [
          country,
          {
            shortName: provinceName?.shortName,
            fullName: provinceName?.fullName,
            code: provinceCode
          },
          {
            shortName: cityName?.shortName,
            fullName: cityName?.fullName,
            code: currentSelected.value.code
          }
        ];
      default: // 处理其他情况
        return [];
    }
  }
  return [];
});
</script>
