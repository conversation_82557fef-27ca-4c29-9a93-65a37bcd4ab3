<template>
  <div
    v-if="show"
    :class="[sidebarRegionList?.length ? 'area-panel' : 'area-panel-single']"
  >
    <div class="area-hd">
      <span class="tit">{{ title ?? '选择战区' }}</span>
      <i class="close" @click="closePopup"></i>
    </div>
    <div ref="scrollAreaRef" class="area-bd">
      <div v-if="!$slots.areaHeader && isShowSelected" class="breadcrumb">
        <span class="label">已选择</span>
        <div class="breadcrumb-box">
          <WgTooltips
            v-if="
              selectedInfo?.sidebarRegion &&
              selectedInfo?.sidebarRegion.code !== selectedInfo?.region?.code
            "
            :text="transFullNameToShort(selectedInfo?.sidebarRegion?.fullName)"
          >
            <span class="breadcrumb-item">
              {{ selectedInfo?.sidebarRegion?.shortName }}
            </span>
          </WgTooltips>
          <WgTooltips
            v-if="selectedInfo?.region?.fullName"
            :text="transFullNameToShort(selectedInfo?.region?.fullName)"
          >
            <span class="breadcrumb-item">{{
              selectedInfo?.region?.shortName
            }}</span>
          </WgTooltips>
        </div>
      </div>
      <slot name="areaHeader" v-bind="{ selectedInfo }"></slot>

      <div class="area-detail">
        <ul v-if="sidebarRegionList" class="area-province">
          <WgTooltips
            v-for="sidebarRegion in sidebarRegionList"
            :key="sidebarRegion.code"
            :text="transFullNameToShort(sidebarRegion.fullName)"
            :open-delay="800"
            :close-delay="0"
          >
            <li
              :data-area-sidebar-region="String(sidebarRegion.code)"
              :class="{
                cur: sidebarRegion.code === selectedInfo?.sidebarRegion?.code
              }"
              @click="switchSidebarRegion(sidebarRegion)"
              >{{ sidebarRegion.shortName }}</li
            >
          </WgTooltips>
        </ul>

        <ul class="area-city">
          <WgTooltips
            v-for="region in regionList"
            :key="region.code"
            :text="transFullNameToShort(region.fullName)"
            :open-delay="800"
            :close-delay="0"
          >
            <li
              :data-area-region="String(region.code)"
              :class="{
                cur:
                  region.code === selectedInfo?.region?.code ||
                  region.code === selectedInfo?.sidebarRegion?.code
              }"
              @click="switchRegion(region)"
              >{{ region.shortName }}</li
            >
          </WgTooltips>
        </ul>
      </div>
    </div>
    <div class="area-fd">
      <!-- <p class="tips-txt">战区每周仅能切换一次</p> -->
      <div class="submit-btn" @click="clickConfirm">确认</div>
      <div class="cancel-btn" @click="closePopup">取消</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import {
  type RegionItem,
  transFullNameToShort
} from '../composable/use-region';
import { WgTooltips } from '@tencent/wg-ui';
import './assets/scss/area-popup.scss';

const props = defineProps<{
  /** 弹窗header文案 */
  title?: string;
  /** 是否显示 */
  show: boolean;
  /** 选中地区 */
  selected?: RegionItem;
  /** 侧边栏选中地区 */
  sidebarSelected?: RegionItem;
  /** 是否显示已选择 */
  isShowSelected?: boolean;
  /** 侧边栏地区列表 */
  sidebarRegionList?: RegionItem[];
  /** 地区列表 */
  regionList: RegionItem[];
}>();

const selectedInfo = ref<{
  /** 侧边栏地区 */
  sidebarRegion?: RegionItem;
  /** 地区 */
  region?: RegionItem;
}>({
  sidebarRegion: props.sidebarSelected,
  region: props.selected
});

// 添加滚动区域ref
const scrollAreaRef = ref<HTMLElement | null>(null);
watch(
  () => scrollAreaRef.value,
  nextRef => {
    if (!nextRef) {
      return;
    }

    selectedInfo.value = {
      sidebarRegion: props.sidebarSelected,
      region: props.selected
    };

    // 自动定位
    if (selectedInfo.value?.sidebarRegion?.code) {
      nextRef
        .querySelector(
          `li[data-area-sidebar-region='${selectedInfo.value.sidebarRegion.code}']`
        )
        ?.scrollIntoView();
    }
    if (selectedInfo.value?.region?.code) {
      nextRef
        .querySelector(
          `li[data-area-region='${selectedInfo.value.region.code}']`
        )
        ?.scrollIntoView();
    }
  }
);

function switchSidebarRegion(region: RegionItem) {
  selectedInfo.value.sidebarRegion = region;
  selectedInfo.value.region = undefined;
  emits('switch-sidebar', region);
}

function switchRegion(region: RegionItem) {
  selectedInfo.value.region = region;
}

const emits = defineEmits<{
  (e: 'update:selected', value?: RegionItem): void;
  (e: 'switch-sidebar', value: RegionItem): void;
  (e: 'update:show', value: boolean): void;
}>();

// 触发emits事件
const closePopup = () => {
  emits('update:show', false);
};

function clickConfirm() {
  emits(
    'update:selected',
    selectedInfo.value.region ||
      selectedInfo.value.sidebarRegion ||
      props.selected
  );
  emits('update:show', false);
}
</script>
