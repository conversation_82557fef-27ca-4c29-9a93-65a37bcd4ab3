.address-box {
  position: relative;
  display: flex;
  align-items: center;

  .area-panel {
    top: 0.3rem;
  }

  .name {
    position: relative;
    color: #fff;
    font-size: 0.14rem;
    font-weight: 700;
    line-height: 0.2rem;
    padding: 0 0.16rem;
    cursor: pointer;
    transition: all 0.2s;

    &::before {
      position: absolute;
      right: 0;
      top: 0.03rem;
      content: '';
      width: 1px;
      height: 0.12rem;
      background-color: rgba($color: #eaebeb, $alpha: 0.2);
    }

    &:first-child {
      padding-left: 0;
    }

    &:hover {
      color: rgba($color: #0ff796, $alpha: 0.6);
    }

    &.cur {
      color: #0ff796;

      &:hover {
        color: #0ff796;
      }
    }
  }

  .address-icon {
    padding: 0 0.06rem;
    width: 0.2rem;
    height: 0.16rem;
    cursor: pointer;
    margin-left: 0.16rem;
    transition: all 0.2s;
    background: url(../images/local-icon.png) no-repeat center;
    background-size: 0.16rem auto;

    &:hover {
      filter: brightness(1.2);
    }
  }
}

.local-tips {
  position: relative;
  width: 0.2rem;
  height: 0.2rem;
  padding: 0.04rem;
  margin-left: 0.22rem;
  background: url(../images/tips-icon.png) no-repeat center;
  background-size: 0.16rem auto;
  cursor: pointer;
  z-index: 9;
  transition: all 0.2s;

  &:hover {
    filter: brightness(1.2);

    .local-hover-tips {
      opacity: 1;
    }
  }
}

.local-hover-tips {
  opacity: 0;
  position: absolute;
  top: -0.62rem;
  left: 0;
  width: 3.02rem;
  height: 1.32rem;
  padding: 0.32rem 0.24rem 0.28rem 0.48rem;
  background: url(../images/hover-tips-bg.png) no-repeat;
  background-size: 100% auto;
  color: rgba(234, 235, 235, 0.8);
  font-size: 0.12rem;
  line-height: 0.2rem;
  z-index: 10;
  transition: all 0.26s ease-in;
  pointer-events: none;
  white-space: pre-line; /* 保留换行符并自动换行 */

  span {
    display: block;
  }

  i {
    color: #0ff796;
  }
}

@media screen and (min-aspect-ratio: 16/9) and (max-height: 1200px) {
  .local-hover-tips {
    width: 3.5rem;
    padding: 0.4rem 0.24rem 0.28rem 0.5rem;
    line-height: 0.22rem;
  }
}

@media screen and (max-width: 1390px) {
  .local-hover-tips {
    width: 3.5rem;
    padding: 0.4rem 0.24rem 0.28rem 0.5rem;
    line-height: 0.22rem;
  }

  .address-box .local-hover-tips {
    top: -0.6rem;
    width: 4.2rem;
    padding-left: 0.63rem;

    span {
      zoom: 0.8;
    }
  }
}

@media screen and (max-height: 750px) {
  .address-box .local-hover-tips {
    top: -0.6rem;
    width: 4.4rem;

    span {
      zoom: 0.8;
    }
  }
}

@media screen and (max-height: 850px) {
  .address-box .local-hover-tips {
    span {
      zoom: 0.88;
    }
  }
}
