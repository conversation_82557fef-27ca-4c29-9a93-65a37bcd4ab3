.breadcrumb {
  padding: 0 0.16rem;
  display: flex;
  align-items: center;
  height: 0.48rem;
  line-height: 0.48rem;

  .label {
    font-size: 0.12rem;
    color: rgba($color: #eaebeb, $alpha: 0.2);
  }

  .breadcrumb-box {
    display: flex;
    align-items: center;
    font-size: 0.12rem;
    // border-radius: 0.06rem;
    margin-left: 0.16rem;
    height: 0.24rem;
    line-height: 0.24rem;
    background-color: #1f2426;

    .breadcrumb-item {
      position: relative;
      display: inline-block;
      padding: 0 0.1rem;
      color: rgba($color: #eaebeb, $alpha: 0.4);

      &::after {
        content: '';
        position: absolute;
        top: 0.1rem;
        right: -0.02rem;
        width: 0;
        height: 0;
        border-top: 3px solid transparent;
        border-left: 3px solid #35393b;
        border-bottom: 3px solid transparent;
      }

      &:last-child {
        &::after {
          display: none;
        }
      }
    }
  }
}

.area-search {
  padding: 0 0.16rem;
  position: relative;
  width: 100%;
  height: 0.3rem;

  input {
    width: 100%;
    height: 100%;
    padding-left: 0.1rem;
    line-height: 0.32rem;
    color: #e3e3e3;
    font-size: 0.14rem;
    background: #13191b;
    backdrop-filter: blur(14.40000057220459px);
    border: 1px solid rgba($color: #eaebeb, $alpha: 0.2);

    &::-webkit-input-placeholder {
      font-size: 0.14rem;
    }

    &:-ms-input-placeholder {
      font-size: 0.14rem;
    }

    &::placeholder {
      font-size: 0.14rem;
    }
  }

  .search-icon {
    position: absolute;
    top: 0.05rem;
    right: 0.22rem;
    width: 0.2rem;
    height: 0.2rem;
    cursor: pointer;
    background: url(../images/search-icon.png) no-repeat;
    background-size: 100% auto;
  }
}

.area-panel {
  position: absolute;
  width: 3.6rem;
  background-color: #181d1f;
  border: 1px solid rgba($color: #eaebeb, $alpha: 0.2);
  z-index: 10;

  /* 自定义整个滚动条 */
  ::-webkit-scrollbar {
    width: 4px !important;
    /* 设置滚动条的宽度 */
    background: #1f2426;
    /* 滚动条的背景色 */
  }

  /* 自定义滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent;
    width: 2px !important;
    /* 轨道的背景色 */
    border-radius: 10px;
    /* 轨道的圆角 */
  }

  /* 自定义滚动条的滑块（thumb） */
  ::-webkit-scrollbar-thumb {
    background: rgba($color: #eaebeb, $alpha: 0.2);
    width: 2px !important;
    /* 滑块的背景色 */
    border-radius: 10px;
    /* 滑块的圆角 */
    // border: 2px solid #ffffff;
    /* 滑块边框 */
  }

  /* 滑块hover效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: rgba($color: #eaebeb, $alpha: 0.4);
    /* 滑块hover时的背景色 */
  }

  .area-hd {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.06rem 0 0.16rem;
    font-size: 0.14rem;
    font-weight: bold;
    color: #eaebeb;
    width: 100%;
    height: 0.46rem;
    line-height: 0.46rem;
    background: url(../images/check-local-bg.png) no-repeat;
    background-size: 100% auto;

    .close {
      display: block;
      width: 0.32rem;
      height: 0.32rem;
      background: url(../images/close-icon.png) no-repeat;
      background-size: 100% auto;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        filter: brightness(1.2);
      }
    }
  }

  .area-detail {
    display: flex;
    background-color: #1f2426;
    // margin-top: 0.2rem;
    height: 2rem;

    .area-province {
      width: 0.8rem;
      height: 100%;
      overflow-y: auto;
      background-color: #181d1f;

      li {
        position: relative;
        height: 0.4rem;
        line-height: 0.4rem;
        background-color: #181d1f;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        padding: 0 0.04rem;
        overflow: hidden;

        &:hover {
          background: rgba(234, 235, 235, 0.2);
        }

        &.cur {
          background-color: #1f2426;

          &::before {
            position: absolute;
            content: '';
            left: 0;
            width: 2px;
            height: 0.2rem;
            top: 0.1rem;
            background-color: #0ff796;
          }
        }
      }
    }

    .area-city {
      padding: 0.12rem 0 0rem 0.1rem;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow-y: auto;
      flex: 1;

      li {
        width: 0.78rem;
        text-align: center;
        height: 0.28rem;
        line-height: 0.28rem;
        margin-right: 0.1rem;
        margin-bottom: 0.1rem;
        border: 1px solid rgba(255, 255, 255, 0.05);
        background: rgba(234, 235, 235, 0.05);
        backdrop-filter: blur(10px);
        cursor: pointer;
        overflow: hidden;
        transition: background 0.2s, border 0.2s;

        &:nth-child(3n) {
          margin-right: 0;
        }

        &:hover {
          border: 1px solid rgba(255, 255, 255, 0.4);
          background: rgba(234, 235, 235, 0.2);
        }

        &.cur {
          color: rgba(15, 247, 150, 0.8);
          border: 1px solid rgba(15, 247, 150, 0.2);
          background: rgba(15, 247, 150, 0.2);
        }
      }
    }
  }

  .area-fd {
    border-top: 1px solid rgba($color: #e3e3e3, $alpha: 0.2);
    text-align: center;
    padding: 0.1rem 0 0.2rem;

    div {
      display: inline-block;
      width: 46%;
      height: 0.32rem;
      line-height: 0.32rem;
      font-size: 0.14rem;
      background-color: red;
      margin: 0.12rem 0.05rem 0 0.05rem;
      background: url(../images/btn-bg.png) no-repeat;
      background-size: 100% 100%;
      cursor: pointer;

      &:hover {
        filter: brightness(1.2);
      }
    }

    .submit-btn {
      color: #0ff796;
      background: url(../images/submit-btn.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}

.area-panel-single {
  position: absolute;
  width: 3rem;
  background-color: #181d1f;
  border: 1px solid rgba($color: #eaebeb, $alpha: 0.2);
  z-index: 10;

  /* 自定义整个滚动条 */
  ::-webkit-scrollbar {
    width: 4px !important;
    /* 设置滚动条的宽度 */
    background: #1f2426;
    /* 滚动条的背景色 */
  }

  /* 自定义滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent;
    width: 2px !important;
    /* 轨道的背景色 */
    border-radius: 10px;
    /* 轨道的圆角 */
  }

  /* 自定义滚动条的滑块（thumb） */
  ::-webkit-scrollbar-thumb {
    background: rgba($color: #eaebeb, $alpha: 0.2);
    width: 2px !important;
    /* 滑块的背景色 */
    border-radius: 10px;
    /* 滑块的圆角 */
    // border: 2px solid #ffffff;
    /* 滑块边框 */
  }

  /* 滑块hover效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: rgba($color: #eaebeb, $alpha: 0.4);
    /* 滑块hover时的背景色 */
  }

  .area-hd {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.06rem 0 0.16rem;
    font-size: 0.14rem;
    font-weight: bold;
    color: #eaebeb;
    width: 100%;
    height: 0.36rem;
    line-height: 0.46rem;
    background: url(../images/check-local-bg.png) no-repeat;
    background-size: 100% auto;

    .close {
      display: block;
      width: 0.32rem;
      height: 0.32rem;
      background: url(../images/close-icon.png) no-repeat;
      background-size: 100% auto;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        filter: brightness(1.2);
      }
    }
  }

  .area-detail {
    display: flex;
    background-color: #1f2426;
    // margin-top: 0.2rem;
    height: 2rem;

    .area-city {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.1rem;
      background-color: #1f2426;
      padding: 0.16rem;
      height: 2rem;
      overflow-y: auto;
      li {
        width: 0.8rem;
        text-align: center;
        height: 0.3rem;
        line-height: 0.3rem;
        border: 1px solid rgba(255, 255, 255, 0.05);
        background: rgba(234, 235, 235, 0.05);
        backdrop-filter: blur(10px);
        cursor: pointer;
        overflow: hidden;
        transition: background 0.2s, border 0.2s;

        &:hover {
          border: 1px solid rgba(255, 255, 255, 0.4);
          background: rgba(234, 235, 235, 0.2);
        }

        &.cur {
          color: rgba(15, 247, 150, 0.8);
          border: 1px solid rgba(15, 247, 150, 0.2);
          background: rgba(15, 247, 150, 0.2);
        }
      }
    }
  }

  .area-fd {
    display: flex;
    align-items: center;
    border-top: 1px solid rgba($color: #e3e3e3, $alpha: 0.2);
    text-align: center;
    gap: 0.13rem;
    padding: 0.1rem 0.12rem 0.2rem;
    justify-content: space-between;

    div {
      display: inline-block;
      width: 46%;
      height: 0.32rem;
      line-height: 0.32rem;
      font-size: 0.14rem;
      background-color: red;
      margin: 0.12rem 0.05rem 0 0.05rem;
      background: url(../images/btn-bg.png) no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
      font-weight: 700;

      &:hover {
        filter: brightness(1.2);
      }
    }

    .submit-btn {
      color: #0ff796;
      background: url(../images/submit-btn.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}
