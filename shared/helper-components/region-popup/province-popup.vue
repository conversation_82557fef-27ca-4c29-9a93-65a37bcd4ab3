<template>
  <AreaPopup
    v-model:show="show"
    v-model:selected="selectedRegionInfo"
    :region-list="[NATION_WIDE_REGION, ...provinceInfo]"
  />
</template>
<script setup lang="ts">
import {
  type RegionItem,
  NATION_WIDE_REGION,
  useRegionInfo
} from '../composable/use-region';
import AreaPopup from './region-popup.vue';

const { provinceInfo } = useRegionInfo();

const show = defineModel<boolean>('show', {
  default: false,
  required: true
});

/** 已选省份信息，默认“全国” */
const selectedRegionInfo = defineModel<RegionItem>('selected', {
  default: NATION_WIDE_REGION
});
</script>
