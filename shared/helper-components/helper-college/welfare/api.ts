import { baseRequest } from '@tencent/wegame-web-sdk';
import { ErrorMessageMap } from '../error-codes';
import { GAME_CONFIG } from './const';

export const enum AccountType {
  unknown = 0,
  qq = 1,
  wx = 2
}

interface BaseRequestParams {
  game_id?: number; //string;
  account_type?: AccountType;
  area?: number;
  from_src?: string;
  openid?: string;
}

export interface RoleInfoItem {
  openid: string;
  area: null;
  name: string;
  avatar: string;
}

/** 任务状态 */
export enum TaskState {
  /** 未完成任务 */
  UNFINISHED = 0,
  /** 已完成任务 */
  FINISHED,
  /** 已领取 */
  CLAIMED,
  /** 未认证无法领取 */
  UNCERTIFIED,
  /** 不符合资格 */
  UNQUALIFIED,
  /** 过期 */
  EXPIRED,
  /** 未创角色 */
  NO_ROLE
}

export interface PerksCard {
  id: string;
  name: string;
  icon: string;
  desc: string;
  state: number;
}

export interface Reward {
  name: string;
  icon: string;
  count: number;
  level: number;
}

export interface TaskItem {
  id: string;
  name: string;
  desc: string;
  reward: Reward;
  state: TaskState;
  count: number;
}

interface NewcomerRewardItem extends Reward {
  desc: string;
}

export interface NewcomerReward {
  id: string;
  name: string;
  desc: string;
  state: number;
  reward: NewcomerRewardItem;
}

// 新手奖励返回
export interface NewcomerRewardResp {
  invitation_code: string;
  reward_info?: NewcomerReward;
}

/**
 * 通用请求端点
 */
const COMMON_FETCH_DATA_ENDPOINT = ['GetAccount', 'GetStudentCertifiedState'];

/**
 * 封装基础请求逻辑
 * @param endpoint API端点
 * @param params 请求参数
 */
const fetchData = async <T>(args: {
  endpoint: string;
  params: BaseRequestParams;
}): Promise<T | null> => {
  try {
    const game_id = args?.params?.game_id || 'default';
    const baseURL = COMMON_FETCH_DATA_ENDPOINT.includes(args.endpoint)
      ? GAME_CONFIG.default.BASE_URL
      : GAME_CONFIG[game_id]?.BASE_URL || GAME_CONFIG.default.BASE_URL;
    const response = await baseRequest<T>({
      url: `/api/v1/${baseURL}/${args.endpoint}`,
      method: 'POST',
      data: args.params
    });
    return response;
  } catch (error) {
    console.error(`${args.endpoint} failed:`, error);
  }
  return null;
};

/**
 * 查询账号id
 */
export const getAccount = async (
  params: BaseRequestParams
): Promise<string> => {
  const response = await fetchData<{ openid: string }>({
    endpoint: 'GetAccount',
    params
  });
  return response?.openid ?? '';
};

/**
 * 查询用户是否已经通过高校认证
 */
export const getStudentCertifiedState = async ({
  from_src = '',
  game_id = 0,
  account_type = 0
}: {
  from_src?: string;
  game_id?: number;
  account_type?: number;
}): Promise<{ certified: 0 | 1; school: string } | null> => {
  try {
    const response = await fetchData<{ certified: 0 | 1; school: string }>({
      endpoint: 'GetStudentCertifiedState',
      params: { from_src, game_id, account_type }
    });
    return response?.certified === 1 ? response : null;
  } catch (error) {
    console.error('GetStudentCertifiedState failed:', error);
  }
  return null;
};

/**
 * 获取高校特权列表
 */
export const getPerksList = async (
  params: BaseRequestParams
): Promise<PerksCard[]> => {
  const response = await fetchData<{ cards: PerksCard[] }>({
    endpoint: 'GetPerksList',
    params
  });
  return response?.cards || [];
};

/**
 * 领取高校特权奖励
 */
export const claimPerksReward = async (
  params: BaseRequestParams & { task_ids: string[] }
): Promise<{ isOk: boolean; retCode: number; msg: string }> => {
  const response = await fetchData<{
    result: { error_code: number; error_message: string };
  }>({ endpoint: 'ClaimPerksReward', params });
  const retCode = response?.result?.error_code ?? -1;
  return {
    isOk: response?.result?.error_code === 0,
    retCode,
    msg: ErrorMessageMap[retCode] || `激活特权失败，请再次尝试(${retCode})`
  };
};

/**
 * 获取每日任务
 */
export const getDailyTasks = async (
  params: BaseRequestParams
): Promise<TaskItem[]> => {
  const response = await fetchData<{ tasks: TaskItem[] }>({
    endpoint: 'GetDailyTasks',
    params
  });
  return response?.tasks || [];
};

/**
 * 领取任务奖励
 */
export const claimReward = async (
  params: BaseRequestParams & { task_id: string }
): Promise<{ isOk: boolean; retCode: number; msg: string }> => {
  const response = await fetchData<{
    result: { error_code: number; error_message: string };
  }>({ endpoint: 'ClaimReward', params });
  const retCode = response?.result?.error_code ?? -1;
  return {
    isOk: response?.result?.error_code === 0,
    retCode,
    msg: ErrorMessageMap[retCode] || `领取奖励失败，请再次尝试(${retCode})`
  };
};

/**
 * 获取首发任务
 */
export const getFirstReleaseTask = async (
  params: BaseRequestParams
): Promise<TaskItem[]> => {
  const response = await fetchData<{ tasks: TaskItem[] }>({
    endpoint: 'GetFirstReleaseTask',
    params
  });
  return response?.tasks || [];
};

/**
 * 领取首发奖励
 */
export const claimFirstReleaseReward = async (
  params: BaseRequestParams & { task_id: string }
): Promise<{ isOk: boolean; retCode: number; msg: string }> => {
  const response = await fetchData<{
    result: { error_code: number; error_message: string };
  }>({ endpoint: 'ClaimFirstReleaseReward', params });
  const retCode = response?.result?.error_code ?? -1;
  return {
    isOk: response?.result?.error_code === 0,
    retCode,
    msg: ErrorMessageMap[retCode] || `领取奖励失败，请再次尝试(${retCode})`
  };
};

/**
 * 获取邀请码
 */
export const getInvitationCode = async (
  params: BaseRequestParams
): Promise<{ code: string; hasRole: number; msg: string }> => {
  const response = await fetchData<{
    result: { error_code: number; error_message: string };
    invitation_code: string;
    state: number;
  }>({
    endpoint: 'GetInvitationCode',
    params
  });
  const errCode = {
    8256901: '未创建角色',
    8256902: '未完成高校认证',
    8256999: '活动已结束'
  };
  return {
    code: response?.invitation_code ?? '',
    hasRole: response?.state ?? 0,
    msg:
      errCode[response?.result.error_code as keyof typeof errCode] || '未知错误'
  };
};

/**
 * 获取邀请任务
 */
export const getInvitationTasks = async (
  params: BaseRequestParams
): Promise<TaskItem[]> => {
  const response = await fetchData<{ tasks: TaskItem[] }>({
    endpoint: 'GetInvitationTasks',
    params
  });
  return response?.tasks || [];
};

/**
 * 获取加码奖励任务
 */
export const getMoreRewardTasks = async (
  params: BaseRequestParams
): Promise<TaskItem[]> => {
  const response = await fetchData<{ tasks: TaskItem[] }>({
    endpoint: 'GetMoreRewardTasks',
    params
  });
  return response?.tasks || [];
};

/**
 * 领取加码任务奖励
 */
export const claimMoreReward = async (
  params: BaseRequestParams & { task_id: string }
): Promise<{ isOk: boolean; retCode: number; msg: string }> => {
  const response = await fetchData<{
    result: { error_code: number; error_message: string };
  }>({ endpoint: 'ClaimMoreReward', params });
  const retCode = response?.result?.error_code ?? -1;
  return {
    isOk: response?.result?.error_code === 0,
    retCode,
    msg: ErrorMessageMap[retCode] || `领取奖励失败，请再次尝试(${retCode})`
  };
};

/**
 * 获取新手奖励
 */
export const getNewcomerReward = async (
  params: BaseRequestParams
): Promise<NewcomerRewardResp | null> => {
  const response = await fetchData<{
    invitation_code: string;
    rewards: NewcomerReward[];
  }>({ endpoint: 'GetNewcomerReward', params });

  if (
    response &&
    Array.isArray(response.rewards) &&
    response.rewards.length > 0
  ) {
    return {
      invitation_code: response.invitation_code || '',
      reward_info: response.rewards[0]
    };
  }
  return null;
};

/**
 * 领取新手奖励
 */
export const claimNewcomerReward = async (
  params: BaseRequestParams & { task_id: string; invitation_code: string }
): Promise<{ isOk: boolean; retCode: number; msg: string }> => {
  const response = await fetchData<{
    result: { error_code: number; error_message: string };
  }>({ endpoint: 'ClaimNewcomerReward', params });
  const retCode = response?.result?.error_code ?? -1;
  return {
    isOk: response?.result?.error_code === 0,
    retCode,
    msg: ErrorMessageMap[retCode] || `领取奖励失败，请再次尝试(${retCode})`
  };
};
