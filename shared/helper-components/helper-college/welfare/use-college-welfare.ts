import { createGlobalState, useAsyncState } from '@vueuse/core';
import { unref, computed, MaybeRef, watch } from 'vue';
import { ACCOUNT_CHANNEL } from '../../interface/account';
import { INVALID_AREA_ID, useAccount } from '../../composable/use-account';
import HelperToast from '../../ui/helper-toast/index';
import {
  getStudentCertifiedState,
  getPerksList,
  claimPerksReward as apiClaimPerksReward,
  getInvitationCode
} from './api';

/**
 * 高校认证全局状态管理钩子
 *
 * 提供高校认证状态、特权列表和任务列表的统一管理
 *
 * @param {MaybeRef<string>} gameId - 游戏ID（响应式引用或字符串）
 * @returns {Object} 包含高校相关状态和方法的对象
 */
export const useCollegeWelfare = createGlobalState(
  (gameId: MaybeRef<string>) => {
    const curGameId = computed(() => unref(gameId));
    const fromSrc = computed(() => `helper_college_${curGameId.value}`);

    const { isLoadedArea, areaId, channel } = useAccount();

    const account = computed(() => {
      if (areaId.value !== INVALID_AREA_ID) {
        const account_type =
          { [ACCOUNT_CHANNEL.QQ]: 1, [ACCOUNT_CHANNEL.WX]: 2 }[channel.value] ??
          0;
        return {
          game_id: Number(curGameId.value),
          area: areaId.value,
          account_type,
          from_src: fromSrc.value
        };
      }
      return { from_src: fromSrc.value, game_id: Number(curGameId.value) };
    });

    // 认证状态
    const {
      state: certifiedState,
      execute: executeCertifiedState,
      isLoading: isLoadingCertifiedState
    } = useAsyncState(
      async () => {
        return await getStudentCertifiedState({
          from_src: fromSrc.value,
          game_id: Number(curGameId.value),
          account_type: account.value?.account_type
        });
      },
      {
        certified: 0,
        school: ''
      },
      {
        immediate: false,
        resetOnExecute: false
      }
    );

    const {
      state: perksList,
      execute: executePerksList,
      isLoading: isLoadingPerksState
    } = useAsyncState(
      async () => {
        return await getPerksList(account.value!);
      },
      [],
      {
        immediate: false,
        resetOnExecute: false
      }
    );

    const claimPerksReward = async (taskIds: string[]) => {
      const resp = await apiClaimPerksReward({
        ...account.value,
        task_ids: taskIds
      });
      if (resp.isOk) {
        executePerksList();
        HelperToast.success('激活特权成功');
      } else {
        HelperToast.error(resp.msg);
         // 8000026 特殊处理领取重复礼包
        if (resp.retCode === 8000026) {
          executePerksList();
        }
      }
    };

    const {
      state: invitationCode,
      execute: executeInvitationCode,
      isLoading: isLoadingInvitationCode
    } = useAsyncState(
      async () => {
        return await getInvitationCode(account.value!);
      },
      null,
      {
        immediate: false
      }
    );

    watch(
      () => [isLoadedArea.value, areaId.value],
      () => {
        if (!isLoadedArea.value) return;
        executeCertifiedState();
        executePerksList();
        executeInvitationCode();
      },
      { immediate: true }
    );

    return {
      isLoadingCertifiedState,
      isLoadingPerksState,
      isLoadingInvitationCode,
      certifiedState,
      collegeAccount: account,
      perksList,
      claimPerksReward,
      invitationCode,
      checkStudentCertifiedState: executeCertifiedState,
      refreshInvitationCode: executeInvitationCode
    };
  }
);
