
const GAME_ID = '2002137';
const GAME_NAME = '鸣潮';
const TEMPLATE_NAME = `helper_components_carousel_${GAME_ID}`;
const TEMPLATE_DISPLAY_NAME = `助手轮播-${GAME_NAME}`;

const spans = document.getElementsByTagName('span');
const gameListSpan = Array.from(spans).find(span => span.textContent.trim() === '定制游戏助手');
const gameSpans = gameListSpan.parentElement.parentElement.getElementsByTagName('span');
const gameSpan = Array.from(gameSpans).find(span => span.textContent.trim() === GAME_NAME);
const pid = gameSpan.id.replace('sidebar_tid_', '');
if(!pid) return; 

// 添加的模板
const tParams = {
  type: 1,
  pid,
  name: TEMPLATE_NAME,
  display_name: TEMPLATE_DISPLAY_NAME
};

// 添加的字段
const eParams = [
  {
    type: 1,
    name: 'title',
    display_name: '标题',
    comment: '',
    required: 1,
    position: 2,
    validation: '',
    value: ''
  },
  {
    type: 9,
    name: 'pic',
    display_name: '图片',
    comment: '',
    required: 1,
    position: 3,
    validation: '',
    value: ''
  },
  {
    type: 1,
    name: 'jump_appid',
    display_name: '跳转appid',
    comment:
      '不填或0为打开浏览器， 50000013商店|50000014个人主页|50000015网吧专区|50000019测试专区|2001068壁纸工坊',
    required: 0,
    position: 4,
    validation: '',
    value: ''
  },
  {
    type: 7,
    name: 'jump_url',
    display_name: '跳转链接',
    comment: '',
    required: 1,
    position: 5,
    validation: '',
    value: ''
  },
  {
    type: 4,
    name: 'jump_url_type',
    display_name: '跳转链接类型',
    comment: '',
    required: 0,
    position: 6,
    validation: '',
    value:
      '[{"name":"系统浏览器","value":"system"},{"name":"WeGame浏览器","value":"new_wegame_window"},{"name":"直接跳转","value":"direct"}]'
  },
  {
    type: 3,
    name: 'order',
    display_name: '展示顺序',
    comment: '升序',
    required: 0,
    position: 7,
    validation: '',
    value: ''
  }
];

// 添加模板
const tid = await addToOss('template', tParams);
// 添加字段
if (tid) {
  eParams.forEach(p => {
    addToOss('element', { ...p, tid });
  });
}
