<template>
  <div
    ref="container"
    :data-appid="appId"
    data-block="carousel"
    class="carousel"
    @mouseenter="enterCarousel"
    @mouseleave="leaveCarousel"
  >
    <div class="carousel-inner">
      <div class="carousel-wrapper" :style="wrapperStyle">
        <div
          v-for="(item, index) in items"
          :key="item.title"
          class="carousel-item"
          :class="{ current: activeIndex === index }"
          @click="clickItem(item, index)"
        >
          <SlideItem :index="index" :item="item">
            <template v-if="$slots.title" #title="{ index, item }">
              <slot
                name="title"
                v-bind="{ index, item, length: items.length }"
              ></slot>
            </template>
            <template v-if="$slots.desc" #desc="{ index, item }">
              <slot
                name="desc"
                v-bind="{ index, item, length: items.length }"
              ></slot>
            </template>
          </SlideItem>
        </div>
      </div>
    </div>

    <template v-if="items.length > 1">
      <!-- 左右翻页箭头 S -->
      <div v-if="navBtnVisible" class="carousel-navigation">
        <div
          :class="[
            'carousel-navigation-button-prev',
            { disabled: isPrevBtnDisabled }
          ]"
          @click="clickPrevBtn"
        ></div>
        <div
          :class="[
            'carousel-navigation-button-next',
            { disabled: isNextBtnDisabled }
          ]"
          @click="clickNextBtn"
        ></div>
      </div>
      <!-- 左右翻页箭头 E -->

      <!-- 翻页文案 S -->
      <div
        v-if="
          [PaginationType.title, PaginationType.index].includes(paginationType)
        "
        class="carousel-thumbs"
      >
        <div
          v-for="(item, index) in items"
          :key="item.title"
          :class="['carousel-thumbs-item', { current: index === activeIndex }]"
          @click="clickPagination(index)"
        >
          <span class="carousel-thumbs-item-text">
            {{
              paginationType === PaginationType.title ? item.title : index + 1
            }}
          </span>
        </div>
      </div>
      <!-- 翻页文案 E -->

      <!-- 翻页圆点 S  -->
      <div
        v-else-if="paginationType === PaginationType.bullets"
        class="carousel-bullets"
      >
        <span
          v-for="(item, index) in items"
          :key="item.title"
          :class="{
            'carousel-bullets-item': true,
            current: index === activeIndex
          }"
          @click="clickPagination(index)"
        >
        </span>
      </div>
      <!-- 翻页圆点 E  -->
    </template>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useIntervalFn } from '@vueuse/core';
import { jumpPage } from '../utils/jump';
import { reportEvent, reportExposure } from '../utils/report';
import { getCarousel } from './api';
import SlideItem from './slide-item.vue';
import type { SlideItem as ISlideItem } from './interface';
import { PaginationType } from './const';

const emits =
  defineEmits<
    (e: 'change-item-index', data: { index: number; item: ISlideItem }) => void
  >();

const props = withDefaults(
  defineProps<{
    appId: string;
    /**
     * 是否显示左右翻页箭头
     */
    navBtnVisible?: boolean;
    /**
     * 是否支持循环
     */
    loop?: boolean;
    /**
     * 翻页类型
     */
    paginationType?: PaginationType;
    /**
     * 最多显示个数
     */
    maxSlides?: number;
    /**
     * oss表名，如果配了则优先用该表名请求数据
     */
    ossDataName?: string;
    /**
     * oss数据，如果配了则优先级最高，则不会用ossDataName或默认appId去读取数据
     */
    ossDataList?: ISlideItem[];
    /**
     * 容器点击回调，如果有值，则clickItem失效
     */
    containerClickedFunc?: Function | null;
    /**
     * 数据上报额外字段，拼到block
     */
    reportExt?: string;
  }>(),
  {
    navBtnVisible: true,
    loop: true,
    paginationType: PaginationType.bullets,
    maxSlides: 5,
    ossDataName: '',
    ossDataList: () => [] as ISlideItem[],
    containerClickedFunc: null,
    reportExt: ''
  }
);

const container = ref(null);
const items = ref<ISlideItem[]>([]);
const activeIndex = ref(0);

const { resume: resumeTimer, pause: pauseTimer } = useIntervalFn(() => {
  activeIndex.value = (activeIndex.value + 1) % items.value.length;
}, 5000);
const enterCarousel = () => pauseTimer();
const leaveCarousel = () => resumeTimer();

const wrapperStyle = computed(() => {
  return { transform: `translate3d(-${activeIndex.value * 100}%, 0, 0)` };
});

const isPrevBtnDisabled = computed(() => {
  return !props.loop && activeIndex.value === 0;
});

const isNextBtnDisabled = computed(() => {
  return !props.loop && activeIndex.value === items.value.length - 1;
});

const clickItem = (item: ISlideItem, index: number) => {
  if (props.containerClickedFunc) {
    props.containerClickedFunc({ index, item });
    return;
  }

  if (!item.jump_url) return;
  let jump_withPtlogin = item.jump_withPtlogin;
  // 2为CF
  if (props?.appId === '2') {
    jump_withPtlogin = '1';
  }
  jumpPage({ ...item, jump_withPtlogin });
  reportEvent({
    appId: props.appId,
    block: 'carousel',
    action: 'click_item',
    ext: index,
    ext3: props?.reportExt || ''
  });
};

const changePagination = (index: number) => {
  pauseTimer();
  activeIndex.value = index;
};

const clickPagination = (index: number) => {
  changePagination(index);
  reportEvent({
    appId: props.appId,
    block: 'carousel',
    action: 'click_pagination',
    ext: index,
    ext3: props?.reportExt || ''
  });
};

const clickPrevBtn = () => {
  if (isPrevBtnDisabled.value) return;
  let index = activeIndex.value - 1;
  if (props.loop) {
    if (index < 0) {
      index = items.value.length - 1;
    }
  }
  index = Math.max(0, index);
  changePagination(index);
  reportEvent({
    appId: props.appId,
    block: 'carousel',
    action: 'click_prev',
    ext: index,
    ext3: props?.reportExt || ''
  });
};

const clickNextBtn = () => {
  if (isNextBtnDisabled.value) return;
  let index = activeIndex.value + 1;
  if (props.loop) {
    if (index > items.value.length - 1) {
      index = 0;
    }
  }
  index = Math.min(items.value.length - 1, index);
  changePagination(index);
  reportEvent({
    appId: props.appId,
    block: 'carousel',
    action: 'click_next',
    ext: index,
    ext3: props?.reportExt || ''
  });
};

const fetchData = async () => {
  if (props.ossDataList?.length) {
    items.value = props.ossDataList.slice(0, props.maxSlides);
  } else {
    items.value = (
      await getCarousel({ appId: props.appId, ossDataName: props.ossDataName })
    ).slice(0, props.maxSlides);
  }

  resumeTimer();
  emits('change-item-index', { index: 0, item: items.value[0] });
};

watch(
  () => activeIndex.value,
  index => {
    emits('change-item-index', { index, item: items.value[index] });
  }
);

onMounted(() => {
  fetchData();
  if (container.value) {
    reportExposure([container.value]);
  }
});
defineExpose({ changePagination });
</script>

<style lang="scss">
@use './assets/scss/carousel.scss';
</style>
