import { baseRequest } from '@tencent/wegame-web-sdk';

import { type SlideItem } from './interface';

export const getCarousel = async ({
  appId,
  ossDataName
}: {
  appId: string;
  ossDataName?: string;
}): Promise<SlideItem[]> => {
  try {
    const resp = (await baseRequest<{
      items: SlideItem[];
    }>({
      url: '/api/rail/web/data_filter/game_config/query',
      method: 'POST',
      data: {
        data_names: ossDataName
          ? ossDataName
          : `helper_components_carousel_${appId}`,
        command: 'list_all',
        params: { start_page: 0, items_per_pager: 50, filters: [] }
      }
    })) as { items?: SlideItem[] };

    const list = (resp?.items || []).sort(
      (a, b) => Number(a.order) - Number(b.order)
    );

    return list;
  } catch (error) {
    console.error(`fetch carousel failed:${error}`);
  }

  return [];
};

/**
 * 从公共OSS获取轮播图数据
 * 
 * @param {Object} [options]
 * @param {string} [options.appId] - appId
 * @param {string} [options.ossDataName=helper_components_carousel] - OSS对应的表名（默认helper_components_carousel）
 * @param {string} [options.ossAppIdKey=app_id] - OSS对应的app_id键名，一般为app_id或game_id（默认app_id）
 * @returns {Promise<Array<SlideItem>>} 返回轮播图数据项的Promise
*/
export const getCarouselByCommonOss = async ({
  appId,
  ossDataName = 'helper_components_carousel',
  ossAppIdKey = 'app_id'
}: {
  appId: string;
  ossDataName?: string;
  ossAppIdKey?: string;
}): Promise<SlideItem[]> => {
  try {
    const resp = (await baseRequest<{
      items: SlideItem[];
    }>({
      url: `/api/rail/web/data_filter/game_config/condition_search`,
      data: {
        data_names: ossDataName,
        search_pair: [{ key: ossAppIdKey, value: appId }],
        stamp: {}
      },
      method: 'POST'
    })) as { items?: SlideItem[] };

    const list = (resp?.items || []).sort(
      (a, b) => Number(a.order) - Number(b.order)
    );

    return list;
  } catch (error) {
    console.error('fetch helper_components_carousel failed:', error);
  }

  return [];
};
