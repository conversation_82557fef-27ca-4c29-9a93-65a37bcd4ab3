<template>
  <VideoItem
    v-if="item?.type === SlideType.video"
    :index="index"
    :title="item.title"
    :src="src"
  />
  <PicItem v-else :index="index" :title="item.title" :src="src" />
  <div class="carousel-slide-words">
    <div class="carousel-slide-title" v-if="$slots.title">
      <slot name="title" v-bind="{ index, item }"></slot>
    </div>
    <div class="carousel-slide-desc" v-if="$slots.desc">
      <slot name="desc" v-bind="{ index, item }"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import PicItem from './pic-item.vue';
import VideoItem from './video-item.vue';
import { SlideType } from './const';
import type { SlideItem } from './interface';

const props = defineProps<{
  index: number;
  item: SlideItem;
}>();

const src = computed(() => {
  if (props.item?.type === SlideType.video && props.item?.source) {
    return props.item.source;
  }
  return props.item.pic;
});
</script>
