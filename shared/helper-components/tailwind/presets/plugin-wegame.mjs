import plugin from 'tailwindcss/plugin';

const wegame = plugin(({ addBase, addComponents, theme }) => {
  addBase({
    ':root': {
      '--color-brand-frame': '#FFDA84',
      '--color-brand': '#FF8F00',

      '--color-state-success': '#39D477',
      '--color-state-success-rgb': '57, 212, 119',
      '--color-state-error': '#EF3F2A',
      '--color-state-error-rgb': '239, 63, 42',
      '--color-state-warning': '#FF7F1C',
      '--color-state-warning-rgb': '255, 127, 28',

      '--btn-gradient':
        'linear-gradient(90deg, #FF9D2B 0%, #FFBC58 49.5%, #FFDA84 100%)',
      '--btn-gradient-hover':
        'linear-gradient(90deg, #FFAD37 0%, #FFC561 49.5%, #FFE073 100%)',
      '--color-popbox-mask-fixed': '#000000 !important'
    },

    body: {
      fontFamily:
        'system-ui, "PingFang SC", "Open Sans", "calibri", "Roboto", verdana, "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", sans-serif',
      color: theme('colors.light'),
      fontSize: theme('fontSize.base'),
      backgroundColor: theme('backgroundColor.main'),
      height: theme('height.screen'),
      width: theme('width.full'),
      cursor: theme('cursor.default'),
      userSelect: 'none',
      overflow: 'hidden',
      position: 'relative'
    },

    '::-webkit-scrollbar': {
      width: '10px !important',
      height: '6px !important',
      background: 'transparent'
    },

    '::-webkit-resizer': {
      background: 'transparent'
    },

    '::-webkit-scrollbar-corner': {
      background: 'transparent'
    },

    '::-webkit-scrollbar-thumb': {
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      transition: 'background-color 0.2s ease-in-out',
      backgroundClip: 'padding-box',
      border: '2px solid transparent',
      borderRadius: '10px'
    },

    '::-webkit-scrollbar-thumb:hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderColor: 'rgba(255, 255, 255, 0.2)'
    },

    '*': {
      boxSizing: 'border-box'
    },

    'button, input, select, textarea': {
      outline: 'none'
    }
  });

  addComponents({
    '.fade-enter-active, .fade-leave-active': {
      transition: 'opacity 0.2s ease-in-out'
    },

    '.fade-enter-from, .fade-leave-to': {
      opacity: '0'
    },

    '.fade-slide-up-enter-active': {
      transition: 'height 0.15s'
    },

    '.fade-slide-up-leave-active': {
      transition: 'height 0.15s'
    },

    '.fade-slide-up-leave-to': {
      overflow: 'hidden',
      height: '0'
    },

    '.popover-up-enter-active': {
      transition: 'transform 0.2s ease-in-out, opacity 0.25s ease-in-out'
    },

    '.popover-up-leave-active': {
      transition: 'transform 0.2s ease-in-out, opacity 0.25s ease-in-out'
    },

    '.popover-up-enter-from': {
      opacity: '0',
      transform: 'translateY(20px)'
    },

    '.popover-up-leave-to': {
      opacity: '0',
      transform: 'translateY(20px)'
    },

    '.popover-fade-enter-active, .popover-fade-leave-active': {
      transition: 'opacity 0.25s ease-in-out'
    },

    '.popover-fade-enter-from, .popover-fade-leave-to': {
      opacity: '0'
    },

    '.popbox-fade-enter-active': {
      transition: 'opacity 0.15s ease-in-out, transform 0.2s ease-out'
    },

    '.popbox-fade-leave-active': {
      transition: 'opacity 0.15s ease-in-out, transform 0.15s ease-in'
    },

    '.popbox-fade-enter-from, .popbox-fade-leave-to': {
      opacity: '0',
      transform: 'translateY(-6px)'
    }
  });
});

export default wegame;
