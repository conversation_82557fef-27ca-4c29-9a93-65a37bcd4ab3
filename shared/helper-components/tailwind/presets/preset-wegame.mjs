import aspectRatio from '@tailwindcss/aspect-ratio';
import animate from 'tailwindcss-animate';
import wegame from './plugin-wegame.mjs';

const themePresetWeGame = {
  fontSize: {
    xs: '10px',
    s: ['12px', '20px'],
    base: ['13px', '21px'],
    m: ['14px', '22px'],
    l: ['16px', '24px'],
    ml: '18px',
    xl: ['20px', '28px'],
    xxl: ['24px', '36px']
  },
  spacing: {
    0: '0px',
    1: '1px',
    px: '1px',
    2: '2px',
    3: '3px',
    4: '4px',
    5: '5px',
    6: '6px',
    7: '7px',
    8: '8px',
    9: '9px',
    10: '10px',
    12: '12px',
    14: '14px',
    16: '16px',
    18: '18px',
    20: '20px',
    24: '24px',
    30: '30px',
    32: '32px',
    36: '36px',
    40: '40px',
    48: '48px',
    50: '50px',
    60: '60px',
    72: '72px'
  },
  extend: {
    fontFamily: {
      sanscnheavy: 'var(--font-family-sanscnheavy)',
      refrigeratorbold: 'var(--font-family-refrigeratorbold)',
      refrigeratorheavy: 'var(--font-family-refrigeratorheavy)'
    },
    backgroundColor: {
      dark: '#070809',
      main: '#101214',
      popover: '#141414',
      'popbox-panel': '#131417',
      light: '#E3E3E3',
      'brand-frame': 'rgba(255, 218, 132, <alpha-value>)',
      success: 'rgba(57, 212, 119, <alpha-value>)',
      error: 'rgba(239, 63, 42, <alpha-value>)',
      warning: 'rgba(255, 127, 28, <alpha-value>)',
      badge: '#E9593F'
    },
    backgroundImage: {
      'brand-linear':
        'linear-gradient(180deg, rgba(255, 197, 97, 0.20) 0%, rgba(255, 255, 255, 0.08) 100%)',
      'brand-linear-hover':
        'linear-gradient(180deg, rgba(255, 197, 97, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%)'
    },
    colors: {
      'brand-frame': '#FFDA84',
      brand: '#FF8F00',
      light: '#E3E3E3',
      icon: '#EBEAE8',
      success: '#39D477',
      error: '#EF3F2A',
      warning: '#FF7F1C',
      badge: '#E9593F'
    },
    textColor: {
      primary: 'rgba(227, 227, 227, <alpha-value>)',
      secondary: 'rgba(33, 33, 33, <alpha-value>)',
      light: 'rgba(227, 227, 227, <alpha-value>)',
      dark: 'rgba(33, 33, 33, <alpha-value>)'
    },
    backgroundSize: {
      'fit-h': 'auto 100%'
    },
    borderColor: {
      light: '#E3E3E3'
    },
    borderRadius: {
      primary: '2px',
      2: '2px',
      4: '4px',
      8: '8px'
    },
    boxShadow: {
      popover: '0px 4px 12px 0px rgba(0, 0, 0, 0.25)',
      tooltip: '0px 6px 8px 0px rgba(0, 0, 0, 0.25)',
      brand: '0px 0px 0px 1px #FF8F00'
    },
    transitionProperty: {
      size: 'width, height',
      bg: 'background'
    },
    transitionDelay: {
      50: '50ms'
    },
    transitionDuration: {
      250: '250ms'
    },
    zIndex: {
      1: 1,
      2: 2,
      10: 10,
      11: 11,
      20: 20,
      100: 100
    },
    opacity: {
      8: 0.08,
      12: 0.12,
      16: 0.16
    },
    screens: {
      'medium-screen': '1400px',
      'large-screen': '1600px'
    }
  }
};

/**
 * 创建 WeGame 的 tailwind 预设
 * @param {*} options
 * @param {string[]} options.content content 字段，默认为 index.html 和所有 js(x)/ts(x)/vue 文件
 * @returns tailwind preset
 */
export const createWeGamePresets = (options = {}) => {
  const { content = [] } = options;
  return {
    content: content?.length
      ? content
      : [
          './*.html',
          './src/**/*.{js,ts,jsx,tsx,vue}',
          './shared/helper-components/**/*.{js,ts,jsx,tsx,vue}'
        ],
    plugins: [aspectRatio, animate, wegame],
    theme: themePresetWeGame,
    safelist: [
      {
        pattern:
          /-(enter-from|enter-active|enter-to|leave-from|leave-active|leave-to)$/
      }
    ]
  };
};
