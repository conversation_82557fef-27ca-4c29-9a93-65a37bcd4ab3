<template>
  <div class="w-full h-full px-30 flex flex-col">
    <div class="relative pt-[38px] pb-12 text-xl font-bold shrink-0 text-primary" :class="$style['popbox-panel-tit']">
      {{ title }}
    </div>
    <div class="flex flex-col h-full flex-1">
      <div class="pt-20 flex-1 h-full overflow-hidden overflow-y-scroll -mr-30 pr-[22px] pb-30">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineProps<{
  title: string;
}>();
</script>

<style lang="scss" module>
.popbox-panel-tit {
  &::after {
    content: '';
    @apply w-full h-20 absolute -bottom-20 z-1 bg-popbox-panel left-0;
    mask-image: linear-gradient(to bottom, #fff, transparent);
  }
}
</style>