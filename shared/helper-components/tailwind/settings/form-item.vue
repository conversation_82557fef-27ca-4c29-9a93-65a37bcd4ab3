<template>
  <div class="relative py-[13px] border-b border-b-white/10 last:border-none inline-flex justify-between items-center">
    <div v-if="label" class="w-fit pr-10">
      <div class="inline-flex items-center">
        <div class="text-m text-primary">{{ label }}</div>
        <WgTooltips v-if="tips" :width="322" dangerouslyUseHTMLString :text="tips" :placement="tipsPlacement"
          no-triangle>
          <div class="w-24 h-24 mx-6 bg-contain hover:scale-[1.1] transition-transform duration-300 cursor-pointer"
            :class="$style['icon-tips']"></div>
        </WgTooltips>
        <!-- <slot name="tips"></slot> -->
      </div>
      <div v-if="describe" class="text-base text-light/60 mt-5">
        {{ describe }}
      </div>
    </div>
    <slot></slot>
  </div>

</template>

<script lang="ts" setup>
import { WgTooltips } from '@tencent/wg-ui';
withDefaults(
  defineProps<{
    label: string;
    describe?: string;
    tips?: string;
    tipsPlacement?: 'top' | 'bottom' | 'left' | 'right';
  }>(),
  {
    tipsPlacement: 'bottom',
  })
</script>


<style lang="scss" module>
.icon-tips {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAL3SURBVHgBzZpPihpBFIdft40RdGTCZKOouAou3OUAmQMkW4+QC0zOkQPkABlwPadQso7oQlFREgiIPQQjUfNeYUnL2F3/XoEfiG3Z3fX7rOqq6kYAjyyXy+Z8Pr8DjwTgCQq/3W5F+FwuN6nVar/BAyF4QIbH4P/o826389YS7AIyfBAEf1er1Y/D4bCgcl8SrF0oGX69Xg/b7faWyieTSQXLqrTN3Z3YBNLCS3xJsAiowkt8SDgL6IaXcEs4CZiGl3BKWAvYhpdwSVgJuIaXcEgYC3CFl7hKGAlwh5e4SGgL+AovsZXQEsgKPx6P7/P5/HswAJcVz/v9/luz2Vwmy20kItUOql8+iqK3GOYjGIDnouMquPk5WU5CKEHfV49rJ1BJhC7hXcBF3s2lcpIwWQCmtoBueKzsCV/fQcGxpR5AA5OWiFzCE41Gg36tBShYLJS7nKErEbqE941OdzoTuKbwEpXEqQtxhR8MBjelUukeK60ky7HyKliS1Z0izvBEsVh8wPAfgJk0iVCGxxFix9FtsIKKzn4YYgiGkAROcD+PxzdxEr2NcCPGz3dhGObK5fIb0BhRTMDWeESpOFmGdcX1ev0RDBmNRq/wfK+P5/3T7/fjiJqBmoOMqN9Op1M5NLJwaclgA4UvFAo0l+QpfK/XG3Y6nZ0YhUiC1h60fZSwvuB8kBaevjsNo9cqkRWeOJsHrk1CFZ54sZRwvSbwuK/4dhLfbDbPYIFOeCL1foBmPJIQOwXBkvPCVqEbXmTLOpGNxGw2+4TD5GkuiOP4S6vVikETk/BE5v2AzTWBou/oBke+MEwJNDENrxSwlbDBJjyh9Xjdt4RteEJ5TyzRHZ3w+ye8Bk53aKpRyCU8Yfxgi3N0cg0vMoAFHBIc4UX9YImLBFd4UTc4YCPBGV7UC46YSHCHF3UCAzoSPsKL+oCJLAlf4UVdwMglCZ/hRT3ATFICHyf+wuC3vsITXv4rkZQgfIUnvP3ZQ0r4DO8dem7T7XZz4JH/gY8scYMKursAAAAASUVORK5CYII=');
}
.disabled {
  :global(.we-checkbox-label) {
    @apply text-light/30 duration-200;
  }
}
:global(.we-tooltips) {
  @apply pointer-events-none !h-fit;
}
</style>