<template>
  <div class="flex-1 h-full flex flex-col gap-y-1 overflow-hidden overflow-y-auto">
    <div v-for="(item, index) in nav" :key="item.type" class="w-full">
      <!-- 点击纯净模式在存在以下设置项的tab下添加该类  ${$style['popbox-menu-item--disabled']} 
          各个设置项均禁用，包括：
          多选框（未选禁用）、单选框、下拉框、输入框 状态 为disabled-->
      <div
        class="w-full text-m cursor-pointer inline-flex items-center relative h-[42px] py-12 px-30 transition-bg duration-200"
        :class="[
          curNavId === index
            ? `${$style['popbox-menu-item--cur']} bg-popbox-panel text-brand font-bold`
            : `text-primary/80 hover:bg-popbox-panel`,
          pureMode === 1
            ? $style['popbox-menu-item--disabled']
            : ''
        ]" @click="updateNavId(index)">
        {{ item.title }}
      </div>
      <div v-if="item?.line" class="h-1 my-10 bg-white/10 mx-24"></div>
    </div>
    <div v-if="withPureMode"
      class="w-full text-m flex items-center relative h-[42px] py-12 pl-30 pr-24 transition-bg duration-200">
      <div class="text-primary/80">纯净模式</div>
      <WgTooltips :width="220" text="勾选纯净模式后，将关闭该应用的助手功能。包括局内工具开关，局内好友设置等。" placement="top" no-triangle>
        <div class="w-24 h-24 mx-6 bg-contain hover:scale-[1.1] transition-transform duration-300 cursor-pointer"
          :class="$style['icon-tips']"></div>
      </WgTooltips>
      <WgSwitch :disabled="isRunning" :model-value="pureMode === 1" @update:model-value="switchPureMode">
      </WgSwitch>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { WgSwitch, WgTooltips } from '@tencent/wg-ui';
import { ref } from 'vue';
const props = defineProps<{
  nav: any;
  curNavId: number;
  withPureMode?: boolean;
  pureMode?: number;
  isRunning?: boolean;
}>();
const pureMode = ref(props.pureMode);
const emits = defineEmits<{ (e: 'update', id: number): void; (e: 'updatePure', id: number): void }>();

const updateNavId = (index: number) => {
  emits('update', index);
}

const switchPureMode = (value: boolean) => {
  pureMode.value = value ? 1 : 0;
  emits('updatePure', value ? 1 : 0);
}

</script>

<style lang="scss" module>
.popbox-menu-item--cur {
  &::after {
    content: '';
    position: absolute;
    width: 9px;
    height: 16px;
    right: 16px;
    background: center/contain no-repeat url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAMAAAAootjDAAAANlBMVEUAAAD/nir/nyz/nSr/lyz/nSv/nzD/niv/nyD/nSz/nSv/nir/nSr/nSr/nSv/mjD/mir/nSu2fYijAAAAEXRSTlMA3yDfIJ8QzxDv78+Af48wMFsns7UAAABaSURBVCjPndE5DoAwEENREyAJO77/ZSkGwSCLCOLyVZY+yLbDc1SjGtWolk6LN8XGLDkLP61Hva3eRrPZ2f7NtsFoKUhwIifkao1M+V3iJZIjF6OpQAUqUDkA8JwNZ43DI68AAAAASUVORK5CYII=');
  }
}

.icon-tips {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAL3SURBVHgBzZpPihpBFIdft40RdGTCZKOouAou3OUAmQMkW4+QC0zOkQPkABlwPadQso7oQlFREgiIPQQjUfNeYUnL2F3/XoEfiG3Z3fX7rOqq6kYAjyyXy+Z8Pr8DjwTgCQq/3W5F+FwuN6nVar/BAyF4QIbH4P/o826389YS7AIyfBAEf1er1Y/D4bCgcl8SrF0oGX69Xg/b7faWyieTSQXLqrTN3Z3YBNLCS3xJsAiowkt8SDgL6IaXcEs4CZiGl3BKWAvYhpdwSVgJuIaXcEgYC3CFl7hKGAlwh5e4SGgL+AovsZXQEsgKPx6P7/P5/HswAJcVz/v9/luz2Vwmy20kItUOql8+iqK3GOYjGIDnouMquPk5WU5CKEHfV49rJ1BJhC7hXcBF3s2lcpIwWQCmtoBueKzsCV/fQcGxpR5AA5OWiFzCE41Gg36tBShYLJS7nKErEbqE941OdzoTuKbwEpXEqQtxhR8MBjelUukeK60ky7HyKliS1Z0izvBEsVh8wPAfgJk0iVCGxxFix9FtsIKKzn4YYgiGkAROcD+PxzdxEr2NcCPGz3dhGObK5fIb0BhRTMDWeESpOFmGdcX1ev0RDBmNRq/wfK+P5/3T7/fjiJqBmoOMqN9Op1M5NLJwaclgA4UvFAo0l+QpfK/XG3Y6nZ0YhUiC1h60fZSwvuB8kBaevjsNo9cqkRWeOJsHrk1CFZ54sZRwvSbwuK/4dhLfbDbPYIFOeCL1foBmPJIQOwXBkvPCVqEbXmTLOpGNxGw2+4TD5GkuiOP4S6vVikETk/BE5v2AzTWBou/oBke+MEwJNDENrxSwlbDBJjyh9Xjdt4RteEJ5TyzRHZ3w+ye8Bk53aKpRyCU8Yfxgi3N0cg0vMoAFHBIc4UX9YImLBFd4UTc4YCPBGV7UC46YSHCHF3UCAzoSPsKL+oCJLAlf4UVdwMglCZ/hRT3ATFICHyf+wuC3vsITXv4rkZQgfIUnvP3ZQ0r4DO8dem7T7XZz4JH/gY8scYMKursAAAAASUVORK5CYII=');
}

.popbox-menu-item--disabled {
  &::after {
    content: '';
    position: absolute;
    right: 16px;
    width: 20px;
    height: 20px;
    background: center/contain no-repeat url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMLSURBVHgB7Zg/bNpAFMYfhgm2WEJIuIUpGcPAwgorrM2auWvXjl2zZs7MGsayloUhWdmgICEhZ4OJP/2+xpbMcbbPxlU68EnI+LDf+/Hu3d27E7nooo9VTlJqPB4XK5XKLb5eb7fbz4VC4epwOBT/Gs3lNmh7Q9sMt5PlcvnabDY3kkKJAefzub3b7Tr5fL7lAxk5yuV+4TJwHMeVBDIGZMTK5XIPjtpyhvD+EBEdmEbUCJBR2+/332DclgyEyLuWZT2YRDMW0HVdZ71ef9XBMddwGeIz4aO+Qy8/HaRCA880dO96kI94Zy5pAcMi5xl/gvGJGAh2WrDTC7ETGUlLIhTWrTD60xSOwrMjDKofzL9gO23TByMuSQFns9ldWM7hn98xKpJA1Wp1A9A+bD6LAol06EoSQHZt3GgF5H1SSAqQAzWSsNWhTzEFZL4oBlwkfF8yggTggDaDbZxbxQRwsVgUYeDIKXLuuV6vD9H+JBlAsrs5yOQYuqXLRUvj8Fa55/Qx4ndes4LkIPOmKR/QXzqjAdG9N4rzF8VwZpDyPocGdS1xgNCn4A2mhxf1gQwhJ1G+tYDIjavgPdZN7UyfEeTRQEHvnc65GgdHiRq1qJ8Lqa4gzEOJA0yqjHPyRCeAwZFFhU2gQaWF5JSmPL+JBUQevClNRiVWGkj85hzBWJYbC4iHZkrTydDPELKh3P+WOEBRhj4iql2CwpQEErYbUb61gDD+qs7wMGwcRVNIXtVqiZsriQPkOonLKNiGf3ofVbOlgGyrBQk3VbopLWyaOSkso2q2FJC6WnOgs2GFGHZ1Ndt0Ou1JRpBBIZrDsLI/dKLW1WwY4V10z5c03a2rJyn6qNVq/bB3QwG9mu1BhWQksT/+nmSV4CCDrY4Ojj6i3j1r20kHLMdY8bCo8JPcW314AnGD39q6Ewi+WyqVHm3bTr/t9PVfb9x9cd2E4S67WM4QB8Rqtcr26CMoL5o9dd8S6QQTP7qbe+PhPzs8UuUfv3GLgO7iom8Hj9/kvRjl2nrW8dtFF320/gDITRBU1xdmbAAAAABJRU5ErkJggg==');
  }
}
</style>
