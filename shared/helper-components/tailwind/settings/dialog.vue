<template>
  <div class="wg-dialog-wrap w-full h-full absolute top-0 left-0 z-100">
    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-2 w-full h-full">
      <div class="wg-dialog wg-dialog-line w-full h-full">
        <!-- 拖拽条，如果当前窗口失焦，添加 drag-blur 类 -->
        <div class="drag-area" :class="{ 'drag-blur': !isWindowFocused }" @mousedown="startDrag"></div>
        <div class="wg-dialog-tools">
          <div class="wg-dialog-minimize" @click="onMinimize"></div>
          <div class="wg-dialog-close" @click="onClose"></div>
        </div>
        <div class="flex w-full h-full text-m">
          <!-- 左侧导航 -->
          <div v-if="$slots.sidebar" class="min-w-fit flex flex-col h-full -mr-1 shrink-0 w-[191px]">
            <slot name="sidebar"></slot>
          </div>
          <!-- 右侧内容 -->
          <div class="flex-1 w-full bg-popbox-panel flex flex-col overflow-hidden">
            <slot name="main"></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import './dialog.scss';
import { useWindowFocus } from '@vueuse/core';
const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'minimize'): void;
  (e: 'drag'): void;
}>();

const isWindowFocused = useWindowFocus();

const startDrag = () => {
  emits('drag');
}
const onClose = () => {
  emits('close');
}
const onMinimize = () => {
  emits('minimize');
}
</script>