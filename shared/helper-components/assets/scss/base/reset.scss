* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  appearance: none;
  outline: none;
}
html,
body {
  user-select: none;
  cursor: default;
  overflow: hidden;
}
img {
  -webkit-user-drag: none;
  user-drag: none;
}
button,
input,
textarea,
select {
  line-height: normal;
  color: var(--color-white);
  font-weight: 400;
}
body {
  width: 100vw;
  height: 100vh;
  font-size: 0.12rem;
  font-family: var(--helper-font-family-system);
  color: var(--color-white);
  line-height: normal;
  font-weight: 400;
  background: #000;
  position: relative;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
}

body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
li,
button,
input,
textarea,
blockquote {
  margin: 0;
  padding: 0;
  word-wrap: break-word;
  word-break: break-word;
}

address,
em,
i,
b {
  font-style: normal;
}

a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
}

ul,
ol,
li {
  list-style: none;
}

img {
  border: 0;
  font-size: 0;
  user-select: none;
  vertical-align: middle;
}

select,
button {
  user-select: none;
}

::-webkit-scrollbar {
  height: 8px;
  width: 8px;
  cursor: pointer;
}

::-webkit-resizer,
::-webkit-scrollbar {
  background: transparent;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--helper-scrollbar-default);
  border-radius: 0;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--helper-scrollbar-hover);
}
