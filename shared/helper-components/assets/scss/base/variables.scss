:root {
  --helper-font-family-system: system-ui, "calibri", "<PERSON><PERSON>", verdana,
    "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei",
    sans-serif;

  // 品牌色
  --helper-color-brand1: #ff7a38;

  // 系统色
  --color-white: #fff;
  --color-black: #000;
  --color-transparent: transparent;

  // 填充
  --helper-color-fill-page-bg: #11181a;
  --helper-color-fill-1: rgb(13, 16, 18, 0.3);

  --helper-scrollbar-default: rgba(255, 255, 255, 0.15);
  --helper-scrollbar-hover: rgba(255, 255, 255, 0.25);

  --helper-navigation-height: 54px;
  --helper-launcher-height: 80px;
  --helper-sidebar-on-width: 182px;
  --helper-sidebar-off-width: 60px;
  --helper-sidebar-hidden-width: 0;

  --sidebar-on-gap: 201px;
  --sidebar-on-gap-ranks: 100px;

  --transition-left-navbar: left 0.3s ease-out;
  --transition-padding-content: padding-left 0.3s ease-out;
  --transition-transform-content: transform 0.3s ease-out;
  --transition-width-content: width 0.3s ease-out;
  --transition-margin-content: margin 0.3s ease-out;
}
body {
  &:not(.helper-v3) {
    --helper-navigation-height: 10px;
  }
}
body.page-out-client {
  --helper-sidebar-on-width: 0;
}

body {
  &:not(.helper-v3):not(.page-out-client) {
    &.sidebar-on {
      --helper-sidebar-on-width: var(--sidebar-on-gap);
    }
    &.sidebar-off {
      --helper-sidebar-on-width: var(--helper-sidebar-hidden-width);
    }
  }
  &.helper-v3 {
    &.sidebar-on {
      --helper-sidebar-on-width: 182px;
    }
    &.sidebar-off {
      --helper-sidebar-on-width: var(--helper-sidebar-off-width);
    }
    &.sidebar-hidden {
      --helper-sidebar-on-width: var(--helper-sidebar-hidden-width);
    }
  }
}
