{"name": "poe2-in-game", "version": "1.0.0", "description": "poe2 in game helper", "type": "module", "main": "index.js", "scripts": {"prepare": "husky install", "preinstall": "npx only-allow pnpm", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "dev": "vite", "build": "vite build"}, "keywords": ["poe2-helper"], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "7.14.6", "@commitlint/cli": "19.8.0", "@tencent/commitlint-plugin-wegame": "^2.2.16", "@tencent/eslint-config-wegame": "^2.5.1", "@tencent/eslint-config-wegame-typescript": "^2.5.1", "@tencent/eslint-plugin-wegame": "^2.5.0", "@tencent/prettier-config-wegame": "^2.5.0", "@tencent/vite-plugin-wegame": "0.3.12", "@testing-library/vue": "^8.1.0", "@types/js-cookie": "^3.0.3", "@types/loadjs": "^4.0.1", "@types/lodash": "^4.14.195", "@types/node": "^18.15.5", "@types/postcss-pxtorem": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.4.0", "@typescript-eslint/parser": "^8.4.0", "@vitejs/plugin-legacy": "^4.0.1", "@vitejs/plugin-vue": "^5.0.5", "@vue/test-utils": "^2.4.6", "@vueuse/head": "^1.1.23", "autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "esbuild": "^0.25.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.24.1", "exceljs": "^4.4.0", "happy-dom": "^17.4.4", "husky": "^9.0.11", "lint-staged": ">=8", "postcss": "^8.4.21", "postcss-preset-env": "^8.0.1", "postcss-pxtorem": "^6.0.0", "prettier": "2", "rollup": "^3.15.0", "rollup-plugin-esbuild": "^5.0.0", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.58.1", "terser": "^5.16.6", "ts-node": "^10.9.1", "typescript": "^5.5.3", "unhead": "1.1.23", "vite": "^6.3.3", "vite-plugin-external": "^1.2.8", "vite-ssg": "^0.22.1", "vite-svg-loader": "^4.0.0", "vitest": "^3.1.1"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@tencent/wegame-web-sdk": "1.1.3", "@vueuse/components": "^10.2.0", "@vueuse/core": "^13.5.0", "dayjs": "^1.11.7", "dompurify": "^3.2.4", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "loadjs": "^4.2.0", "lodash": "^4.17.21", "marked": "^15.0.7", "mermaid": "^11.9.0", "mitt": "^3.0.1", "pinia": "^2.0.33", "postcss-pxtorem": "^6.0.0", "vite-plugin-mock": "^3.0.2", "vue": "3.4.31", "vue-router": "^4.0.15"}, "lint-staged": {"*.{ts,js,vue}": ["eslint --fix"]}, "engines": {"node": ">=16", "pnpm": ">=8"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"vite": "4", "rollup": "3", "eslint": "8", "prettier": "2", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/eslint-plugin": "5", "@typescript-eslint/parser": "5"}}}, "browserslist": ["Chrome > 90", "not IE 11"]}