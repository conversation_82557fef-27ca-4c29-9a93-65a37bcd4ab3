import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath } from 'node:url';
import { resolve } from 'node:path';

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: ['./tests/unit/setup.ts'],
    alias: {
      '@src': fileURLToPath(new URL('./src', import.meta.url)),
      assets: fileURLToPath(new URL('./src/assets', import.meta.url))
    }
  },
  resolve: {
    alias: {
      '@src': resolve(__dirname, './src'),
      shared: resolve(__dirname, './shared'),
      assets: resolve(__dirname, './src/assets')
    }
  }
});
