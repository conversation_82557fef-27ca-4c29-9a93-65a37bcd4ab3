{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "lib": ["ESNext", "DOM"],
    "moduleResolution": "Node",
    "strict": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "noUnusedLocals": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "outDir": "dist",
    "allowJs": true,
    "baseUrl": "./",
    "jsx": "preserve",
    "isolatedModules": true,
    "paths": {
      "@src/*": ["./src/*"],
      "assets/*": ["./src/assets/*"],
      "@preview/*": ["./preview/*"],
      "shared/*": ["./shared/*"]
    }
  },
  "include": [
    "vite.config.mts",
    "vitest.config.mts",
    "./shared",
    "./src",
    "shims-vue.d.ts",
    "global.d.ts",
    "spec",
    "./scripts/",
    "./tests"
  ],
  "exclude": ["dist"],
  "ts-node": {
    // these options are overrides used only by ts-node
    "compilerOptions": {
      "target": "es6",
      "module": "commonjs",
      "esModuleInterop": true
    }
  }
}
