import { MockMethod } from 'vite-plugin-mock';

// BD 相关接口 URL
const BD_BASE_URL = '/api/v1/wegame.pallas.poe2.Poe2BaseInfo';
const GET_BD_LIST_URL = `${BD_BASE_URL}/GetBdList`;
const GET_BD_DETAIL_URL = `${BD_BASE_URL}/GetBdDetail`;
const RECOMMEND_BD_REPORT_URL = `${BD_BASE_URL}/RecommendBDReport`;
const GET_RECOMMEND_BD_URL = `${BD_BASE_URL}/GetRecommendBD`;

export default [
  // BD 列表接口
  {
    url: GET_BD_LIST_URL,
    method: 'post',
    response: () => {
      return {
        "result": {
          "error_code": 0,
          "error_message": "succ"
        },
        "bd_list": [
          {
            "bd_id": "1-1-1",
            "class_id": "Ranger",
            "ascendancy_id": "Amazon",
            "title": "狐狐鹦鹉血爆法BD推荐",
            "author": "狐狐鹦鹉",
            "tags": [
              "攻坚",
              "Farm"
            ]
          },
          {
            "bd_id": "1-1-2",
            "class_id": "Ranger",
            "ascendancy_id": "Amazon",
            "title": "sss",
            "author": "ss",
            "tags": [
              "攻坚",
              "Farm"
            ]
          }
        ],
        "translate": {
          "Amazon": {
            "name": "亚马逊",
            "description": ""
          },
          "Ranger": {
            "name": "游侠",
            "description": "一发精准的箭矢能够解决所有问题。有了轻快的步伐和鹰隼的锐眼，只需要张弓搭箭，胆敢追逐我的人唯有一个下场。 "
          }
        }
      };
    }
  },
  // BD 详情接口
  {
    url: GET_BD_DETAIL_URL,
    method: 'post',
    response: () => {
      return {
        "result": {
          "error_code": 0,
          "error_message": "succ"
        },
        "bd_list": [
          {
            "ascendancy_class": "Monk",
            "author": "狐狐鹦鹉",
            "bd_id": "1-1-1",
            "class": "Amazon",
            "equipment_complex": "首选：<br>点伤加成|暴击率|暴击伤害|满抗性|35%移动速度<br>次选：<br>49精魂|高能量护盾|生命值|能力值<br>可选：<br>眩晕门槛|击杀生命恢复|击杀魔力恢复",
            "items": {},
            "output_method": "攻坚手法：<br>闪电漩涡|炼狱战吼|斜掠|洞穿<br>输出手法：<br>闪电战矛",
            "passive_skill": "layton 的url",
            "passive_skill_point": '70',
            "passive_skill_stone": [
              {
                "label": "首选",
                "priority": 1,
                "items": [
                  "以暴击施加的伤害型异常状态伤害增加",
                  "伤害型异常状态加速",
                  "全域暴击几率（蓝宝石）",
                  "攻击的暴击几率（绿宝石）",
                  "暴击伤害（蓝宝石）",
                  "攻击的暴击伤害（绿宝石）",
                  "对稀有和传奇敌人的伤害"
                ]
              },
              {
                "label": "次选",
                "priority": 2,
                "items": [
                  "流血幅度",
                  "异常状态幅度",
                  "伤害型异常状态幅度",
                  "攻击伤害",
                  "长锋伤害",
                  "晕眩门槛相关词条"
                ]
              },
              {
                "label": "可选",
                "priority": 3,
                "items": [
                  "攻击速度",
                  "长锋攻击速度（提升刷图手感）",
                  "捷光环伤害（对中低配很有用）",
                  "击杀回血击杀回蓝"
                ]
              }
            ],
            "skills": [],
            "tags": [
              "攻坚",
              "Farm"
            ],
            "titile": "狐狐鹦鹉血爆法BD推荐"
          }
        ]
      };
    }
  },
  // 推荐BD上报记录接口
  {
    url: RECOMMEND_BD_REPORT_URL,
    method: 'post',
    response: () => {
      return {
        "result": {
          "error_code": 0,
          "error_message": "succ"
        }
      };
    }
  },
  // 获取推荐BD接口
  {
    url: GET_RECOMMEND_BD_URL,
    method: 'post',
    response: () => {
      return {
        "result": {
          "error_code": 0,
          "error_message": "succ"
        },
        "bd_id": ""
      };
    }
  }
] as MockMethod[];
