module.exports = {
  extends: [
    '@tencent/wegame',
    '@tencent/wegame-typescript',
    'plugin:prettier/recommended'
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    extraFileExtensions: ['.vue']
  },
  plugins: ['@typescript-eslint'],
  rules: {
    'no-console': ['error', { allow: ['warn', 'error'] }],
    camelcase: 'off',
    'func-style': 'off',
    'id-length': 'off',
    'import/no-commonjs': 'warn',
    'no-template-curly-in-string': 'off',
    '@tencent/wegame/no-raw-text-in-template': 'off',
    '@tencent/wegame/no-chinese': 'off',
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': 'error'
  },
  overrides: [
    {
      files: ['**/*.vue'],
      rules: {
        'vue/no-multiple-template-root': ['off'],
        'vue/multi-word-component-names': ['off']
      }
    },
    {
      files: [
        'src/debug/**/*',
        '**/zh[-_]CN.ts',
        '**/zh[-_]HK.ts',
        'src/Daily.vue'
      ],
      rules: {
        '@tencent/wegame/no-chinese': ['off']
      }
    }
  ]
};
